import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Shield, Eye, Cookie, Database, Lock, UserCheck } from "lucide-react";
import SEO from "@/components/SEO";

const Privacy = () => {
  const privacySections = [
    {
      icon: Database,
      title: "Information We Collect",
      content: [
        "GPU hardware specifications and performance data during testing",
        "Browser information and WebGL capabilities for compatibility",
        "Anonymous usage statistics to improve our free testing service",
        "Voluntary feedback and suggestions submitted through our contact form"
      ]
    },
    {
      icon: Eye,
      title: "How We Use Information",
      content: [
        "Provide accurate GPU stress testing and benchmark results",
        "Improve our free online testing algorithms and compatibility",
        "Generate anonymous performance statistics and hardware trends",
        "Respond to user inquiries and provide technical support"
      ]
    },
    {
      icon: Shield,
      title: "Data Protection",
      content: [
        "All GPU testing is performed locally in your browser",
        "No personal identification information is collected or stored",
        "Performance data is anonymized and cannot be traced to individual users",
        "We use industry-standard security measures to protect collected data"
      ]
    },
    {
      icon: <PERSON><PERSON>,
      title: "Cookies and Tracking",
      content: [
        "Essential cookies for website functionality and user preferences",
        "Anonymous analytics cookies to understand website usage patterns",
        "No third-party tracking cookies or advertising networks",
        "Users can disable cookies through browser settings without affecting GPU testing"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <SEO 
        title="Privacy Policy - GPU Stress Test Data Protection"
        description="Learn how GPU Stress Test protects your privacy during GPU testing. We prioritize data security and transparency in our free online graphics card testing service."
        keywords="privacy policy, data protection, gpu testing privacy, graphics card test security, webgl privacy"
        canonical="https://gpustresstes.com/privacy"
      />
      <Navbar />
      
      <main className="pt-20">
        {/* Header Section */}
        <section className="py-20 bg-gradient-to-b from-background to-card/20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-card border border-primary/20 rounded-full text-sm font-medium shadow-glow-primary mb-6">
              <Lock className="w-4 h-4 mr-2 text-primary" />
              Privacy Protection
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">Privacy Policy</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Your privacy is important to us. This policy explains how we collect, use, and protect 
              your information when using our free online GPU stress testing service.
            </p>
            <div className="mt-6 text-sm text-muted-foreground">
              Last updated: {new Date().toLocaleDateString()}
            </div>
          </div>
        </section>

        {/* Privacy Sections */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="space-y-8">
              {privacySections.map((section, index) => (
                <Card key={index} variant="tech" className="group hover:shadow-glow-primary transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-gradient-primary rounded-lg shadow-glow-primary group-hover:shadow-glow-secondary transition-all duration-300">
                        <section.icon className="h-6 w-6 text-primary-foreground" />
                      </div>
                      <CardTitle className="text-2xl">{section.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {section.content.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-muted-foreground leading-relaxed">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Additional Privacy Information */}
        <section className="py-16 bg-gradient-to-b from-card/20 to-background">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-2 gap-8">
              <Card variant="glow" className="p-6">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <UserCheck className="h-6 w-6 text-primary" />
                    <CardTitle className="text-xl">Your Rights</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-muted-foreground">
                    You have the right to know what information we collect and how it's used. 
                    Since our GPU testing service operates entirely in your browser with minimal 
                    data collection, you maintain full control over your testing data.
                  </p>
                  <p className="text-muted-foreground">
                    You can request information about any data we may have collected, 
                    or ask us to delete any information associated with your usage of our service.
                  </p>
                </CardContent>
              </Card>

              <Card variant="glow" className="p-6">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <Shield className="h-6 w-6 text-primary" />
                    <CardTitle className="text-xl">Data Security</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-muted-foreground">
                    Our free online GPU stress test is designed with privacy in mind. 
                    All testing computations are performed locally on your device, 
                    ensuring your hardware information remains private.
                  </p>
                  <p className="text-muted-foreground">
                    We implement appropriate technical and organizational measures to 
                    protect any information we collect against unauthorized access, 
                    alteration, disclosure, or destruction.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Card variant="tech" className="p-8">
              <h2 className="text-2xl font-bold mb-4 gradient-text">Questions About Privacy?</h2>
              <p className="text-muted-foreground mb-6">
                If you have any questions about this privacy policy or our data practices, 
                please don't hesitate to contact us. We're committed to transparency and 
                protecting your privacy while providing the best free GPU testing experience.
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>Email: <EMAIL></p>
                <p>Response time: Within 48 hours</p>
              </div>
            </Card>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Privacy;