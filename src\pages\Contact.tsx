import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Mail, MessageSquare, Clock, HeadphonesIcon } from "lucide-react";
import SEO from "@/components/SEO";

const Contact = () => {
  return (
    <div className="min-h-screen bg-background">
      <SEO 
        title="Contact GPU Stress Test - Technical Support & Feedback"
        description="Get in touch with GPU Stress Test for technical support, feedback, or questions about our free online GPU testing tools. We're here to help with your graphics card testing needs."
        keywords="gpu testing support, graphics card help, gpu stress test contact, technical support, gpu testing feedback"
        canonical="https://gpustresstes.com/contact"
      />
      <Navbar />
      
      <main className="pt-20">
        {/* Header Section */}
        <section className="py-20 bg-gradient-to-b from-background to-card/20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-card border border-primary/20 rounded-full text-sm font-medium shadow-glow-primary mb-6">
              <Mail className="w-4 h-4 mr-2 text-primary" />
              Get in Touch
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">Contact Us</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Have questions about our free online GPU stress testing tool? Need technical support 
              or want to provide feedback? We're here to help you get the most out of your GPU testing experience.
            </p>
          </div>
        </section>

        {/* Contact Form & Info */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <Card variant="tech" className="p-8">
                <CardHeader>
                  <CardTitle className="text-2xl gradient-text">Send us a Message</CardTitle>
                  <p className="text-muted-foreground">
                    Fill out the form below and we'll get back to you within 24 hours.
                  </p>
                </CardHeader>
                <CardContent>
                  <form className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">First Name</Label>
                        <Input 
                          id="firstName" 
                          placeholder="Your first name"
                          className="bg-card/50 border-primary/20 focus:border-primary"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input 
                          id="lastName" 
                          placeholder="Your last name"
                          className="bg-card/50 border-primary/20 focus:border-primary"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input 
                        id="email" 
                        type="email" 
                        placeholder="<EMAIL>"
                        className="bg-card/50 border-primary/20 focus:border-primary"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="subject">Subject</Label>
                      <Input 
                        id="subject" 
                        placeholder="What's this about?"
                        className="bg-card/50 border-primary/20 focus:border-primary"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="message">Message</Label>
                      <Textarea 
                        id="message" 
                        placeholder="Tell us more about your question or feedback..."
                        rows={6}
                        className="bg-card/50 border-primary/20 focus:border-primary resize-none"
                      />
                    </div>
                    
                    <Button variant="hero" size="lg" className="w-full">
                      <MessageSquare className="w-5 h-5 mr-2" />
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <div className="space-y-8">
                <Card variant="glow" className="p-6">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-gradient-primary rounded-lg shadow-glow-primary">
                        <HeadphonesIcon className="h-6 w-6 text-primary-foreground" />
                      </div>
                      <CardTitle className="text-xl">Technical Support</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <p className="text-muted-foreground">
                      Need help with GPU stress testing, performance analysis, or troubleshooting? 
                      Our technical team is ready to assist you.
                    </p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-primary" />
                        <span className="text-sm"><EMAIL></span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-primary" />
                        <span className="text-sm">Response time: Within 24 hours</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card variant="glow" className="p-6">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-gradient-secondary rounded-lg shadow-glow-secondary">
                        <MessageSquare className="h-6 w-6 text-secondary-foreground" />
                      </div>
                      <CardTitle className="text-xl">General Inquiries</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <p className="text-muted-foreground">
                      Questions about our service, partnerships, or general feedback about 
                      our free GPU testing platform.
                    </p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-secondary" />
                        <span className="text-sm"><EMAIL></span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-secondary" />
                        <span className="text-sm">Response time: Within 48 hours</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card variant="tech" className="p-6">
                  <CardHeader>
                    <CardTitle className="text-xl">Common Support Topics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-accent rounded-full"></div>
                        <span>GPU stress test not starting or loading</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        <span>Temperature monitoring and safety concerns</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-secondary rounded-full"></div>
                        <span>Benchmark result interpretation</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-accent rounded-full"></div>
                        <span>Browser compatibility issues</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        <span>GPU detection and driver problems</span>
                      </li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Additional Resources */}
        <section className="py-16 bg-gradient-to-b from-card/20 to-background">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-2xl font-bold mb-8 gradient-text">
              Before You Contact Us
            </h2>
            <div className="grid md:grid-cols-3 gap-6">
              <Card variant="tech" className="p-6">
                <h3 className="font-semibold mb-3">Check Our FAQ</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Most common questions about GPU stress testing are answered in our comprehensive FAQ section.
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  View FAQ
                </Button>
              </Card>
              
              <Card variant="tech" className="p-6">
                <h3 className="font-semibold mb-3">Testing Guide</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Learn how to properly use our GPU stress testing tool with our detailed guide and best practices.
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  Read Guide
                </Button>
              </Card>
              
              <Card variant="tech" className="p-6">
                <h3 className="font-semibold mb-3">Try the Test</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Many issues can be resolved by simply trying our free GPU stress test with updated drivers.
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  Start Test
                </Button>
              </Card>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Contact;