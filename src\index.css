@tailwind base;
@tailwind components;
@tailwind utilities;

/* GPU Stress Test Design System - Tech/Gaming Aesthetic
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Dark tech theme */
    --background: 222 22% 8%;
    --foreground: 210 100% 98%;

    --card: 222 22% 10%;
    --card-foreground: 210 100% 98%;

    --popover: 222 22% 12%;
    --popover-foreground: 210 100% 98%;

    /* Neon tech colors */
    --primary: 195 100% 50%;
    --primary-foreground: 222 22% 8%;
    --primary-glow: 195 100% 70%;

    --secondary: 280 100% 70%;
    --secondary-foreground: 222 22% 8%;

    --muted: 222 22% 15%;
    --muted-foreground: 210 20% 70%;

    --accent: 120 100% 50%;
    --accent-foreground: 222 22% 8%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 100% 98%;

    --border: 222 22% 20%;
    --input: 222 22% 15%;
    --ring: 195 100% 50%;

    /* GPU tech gradients */
    --gradient-primary: linear-gradient(135deg, hsl(195 100% 50%), hsl(280 100% 70%));
    --gradient-secondary: linear-gradient(45deg, hsl(120 100% 50%), hsl(195 100% 50%));
    --gradient-hero: linear-gradient(135deg, hsl(222 22% 8%) 0%, hsl(222 22% 12%) 50%, hsl(222 22% 8%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(222 22% 10%) 0%, hsl(222 22% 15%) 100%);
    
    /* Tech effects */
    --glow-primary: 0 0 30px hsl(195 100% 50% / 0.3);
    --glow-secondary: 0 0 30px hsl(280 100% 70% / 0.3);
    --glow-accent: 0 0 30px hsl(120 100% 50% / 0.3);
    --shadow-tech: 0 8px 32px hsl(195 100% 50% / 0.1);
    
    /* Animations */
    --transition-tech: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Light mode - keeping consistent with tech theme */
  .light {
    --background: 210 100% 98%;
    --foreground: 222 22% 8%;
    --card: 210 100% 100%;
    --card-foreground: 222 22% 8%;
    --primary: 195 100% 40%;
    --primary-foreground: 210 100% 98%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    background: var(--gradient-hero);
    min-height: 100vh;
  }

  /* Tech glow effects */
  .glow-primary {
    box-shadow: var(--glow-primary);
  }
  
  .glow-secondary {
    box-shadow: var(--glow-secondary);
  }
  
  .glow-accent {
    box-shadow: var(--glow-accent);
  }

  /* Gradient text */
  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Custom animations */
  @keyframes tech-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  .animate-tech-pulse {
    animation: tech-pulse 2s ease-in-out infinite;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
  }
}