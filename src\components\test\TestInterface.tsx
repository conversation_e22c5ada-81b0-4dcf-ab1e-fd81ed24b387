import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Monitor, 
  Cpu, 
  HardDrive, 
  Thermometer, 
  Zap, 
  Settings, 
  Info,
  AlertTriangle,
  CheckCircle,
  Play
} from 'lucide-react';
import GPUStressTest from './GPUStressTest';
import GPUStressTestOnline from './GPUStressTestOnline';

const TestInterface: React.FC = () => {
  const systemInfo = {
    browser: navigator.userAgent.includes('Chrome') ? 'Chrome' : 
             navigator.userAgent.includes('Firefox') ? 'Firefox' : 
             navigator.userAgent.includes('Safari') ? 'Safari' : 'Unknown',
    webGL: (() => {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return gl ? 'Supported' : 'Not Supported';
    })(),
    platform: navigator.platform,
    cores: navigator.hardwareConcurrency || 'Unknown',
    memory: (navigator as any).deviceMemory ? `${(navigator as any).deviceMemory}GB` : 'Unknown'
  };

  const requirements = [
    {
      item: 'WebGL Support',
      status: systemInfo.webGL === 'Supported',
      description: 'Required for GPU testing'
    },
    {
      item: 'Modern Browser',
      status: ['Chrome', 'Firefox', 'Safari'].includes(systemInfo.browser),
      description: 'Chrome, Firefox, or Safari recommended'
    },
    {
      item: 'Hardware Acceleration',
      status: true, // Assume enabled
      description: 'GPU acceleration should be enabled'
    }
  ];

  const testFeatures = [
    {
      icon: Monitor,
      title: 'Real-time Visualization',
      description: 'Watch your GPU performance with live 3D graphics rendering and complex shader operations.'
    },
    {
      icon: Thermometer,
      title: 'Temperature Monitoring',
      description: 'Simulated temperature tracking based on performance metrics and workload intensity.'
    },
    {
      icon: Cpu,
      title: 'Performance Analysis', 
      description: 'Detailed FPS analysis, frame time measurements, and performance scoring.'
    },
    {
      icon: HardDrive,
      title: 'Memory Testing',
      description: 'VRAM usage simulation and memory bandwidth stress testing capabilities.'
    },
    {
      icon: Zap,
      title: 'Power Estimation',
      description: 'Estimated power consumption based on GPU utilization and performance metrics.'
    },
    {
      icon: Settings,
      title: 'Multiple Test Modes',
      description: 'Choose from Light, Medium, Heavy, or Extreme test configurations.'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-background via-card/10 to-background" id="test">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-card border border-primary/20 rounded-full text-sm font-medium shadow-glow-primary mb-6">
            <Play className="w-4 h-4 mr-2 text-primary" />
            Free Online GPU Testing Tool
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="gradient-text">GPU Stress Test</span> - Test Your Graphics Card
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Run comprehensive GPU stress tests directly in your browser. No software installation required. 
            Test graphics card performance, monitor temperatures, and benchmark your GPU against thousands of other systems.
          </p>
        </div>

        <Tabs defaultValue="online" className="space-y-8">
          <div className="flex justify-center px-4">
            <div className="w-full max-w-2xl">
              {/* Mobile Layout */}
              <div className="md:hidden">
                <TabsList className="grid grid-cols-2 w-full gap-2 p-2 h-auto">
                  <TabsTrigger value="online" className="text-xs px-3 py-3 h-auto flex-col space-y-1">
                    <span className="font-medium">Online Test</span>
                  </TabsTrigger>
                  <TabsTrigger value="test" className="text-xs px-3 py-3 h-auto flex-col space-y-1">
                    <span className="font-medium">GPU Test</span>
                  </TabsTrigger>
                </TabsList>
                <TabsList className="grid grid-cols-2 w-full gap-2 p-2 h-auto mt-2">
                  <TabsTrigger value="system" className="text-xs px-3 py-3 h-auto flex-col space-y-1">
                    <span className="font-medium">System Info</span>
                  </TabsTrigger>
                  <TabsTrigger value="features" className="text-xs px-3 py-3 h-auto flex-col space-y-1">
                    <span className="font-medium">Features</span>
                  </TabsTrigger>
                </TabsList>
              </div>
              
              {/* Desktop Layout */}
              <div className="hidden md:block">
                <TabsList className="grid grid-cols-4 w-full">
                  <TabsTrigger value="online" className="text-sm">GPU Online Test</TabsTrigger>
                  <TabsTrigger value="test" className="text-sm">GPU Test</TabsTrigger>
                  <TabsTrigger value="system" className="text-sm">System Info</TabsTrigger>
                  <TabsTrigger value="features" className="text-sm">Features</TabsTrigger>
                </TabsList>
              </div>
            </div>
          </div>

          <TabsContent value="test" className="space-y-8">
            {/* System Requirements Check */}
            <Card variant="tech">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Info className="w-6 h-6 text-primary" />
                  <span>System Compatibility Check</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  {requirements.map((req, index) => (
                    <div key={index} className="flex items-start space-x-3 p-4 bg-gradient-card rounded-lg border border-primary/10">
                      {req.status ? (
                        <CheckCircle className="w-5 h-5 text-green-500 mt-1 flex-shrink-0" />
                      ) : (
                        <AlertTriangle className="w-5 h-5 text-red-500 mt-1 flex-shrink-0" />
                      )}
                      <div className="flex-1">
                        <h4 className="font-semibold text-sm">{req.item}</h4>
                        <p className="text-xs text-muted-foreground mt-1">{req.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
                {requirements.every(req => req.status) ? (
                  <div className="mt-4 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                    <p className="text-green-400 text-sm text-center">
                      ✅ Your system is ready for GPU stress testing!
                    </p>
                  </div>
                ) : (
                  <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                    <p className="text-red-400 text-sm text-center">
                      ⚠️ Some system requirements are not met. Testing may not work properly.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* GPU Stress Test Component */}
            <GPUStressTest />

            {/* Test Information */}
            <Card variant="tech">
              <CardHeader>
                <CardTitle>Important Testing Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-foreground mb-3 flex items-center">
                      <AlertTriangle className="w-4 h-4 mr-2 text-yellow-500" />
                      Before Testing
                    </h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Close unnecessary browser tabs and applications</li>
                      <li>• Ensure your laptop is plugged in or has sufficient battery</li>
                      <li>• Make sure your system has adequate cooling</li>
                      <li>• Monitor your system during intensive tests</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-3 flex items-center">
                      <Info className="w-4 h-4 mr-2 text-blue-500" />
                      Test Accuracy
                    </h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>• Results may vary based on browser and system configuration</li>
                      <li>• Temperature readings are estimated based on performance</li>
                      <li>• For professional benchmarking, use dedicated software</li>
                      <li>• This test is designed for general GPU health checking</li>
                    </ul>
                  </div>
                </div>
                <Separator />
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">
                    <strong>Disclaimer:</strong> This browser-based GPU stress test is intended for educational and diagnostic purposes. 
                    Results are approximate and should not be used as the sole basis for hardware decisions. 
                    Always monitor your system temperature and stop testing if you notice any unusual behavior.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="online" className="space-y-8">
            {/* GPU Stress Test Online Component */}
            <GPUStressTestOnline />
          </TabsContent>

          <TabsContent value="system" className="space-y-8">
            <Card variant="tech">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Monitor className="w-6 h-6 text-primary" />
                  <span>Detected System Information</span>
                </CardTitle>
                <CardDescription>
                  Information about your current system and browser capabilities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Browser:</span>
                      <Badge variant="outline">{systemInfo.browser}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">WebGL Support:</span>
                      <Badge variant={systemInfo.webGL === 'Supported' ? 'default' : 'destructive'}>
                        {systemInfo.webGL}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Platform:</span>
                      <Badge variant="outline">{systemInfo.platform}</Badge>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">CPU Cores:</span>
                      <Badge variant="outline">{systemInfo.cores}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Device Memory:</span>
                      <Badge variant="outline">{systemInfo.memory}</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="features" className="space-y-8">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {testFeatures.map((feature, index) => (
                <Card key={index} variant="glow" className="group hover:shadow-glow-primary transition-all duration-300">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-gradient-secondary rounded-lg shadow-glow-secondary group-hover:shadow-glow-accent transition-all duration-300">
                        <feature.icon className="h-6 w-6 text-secondary-foreground" />
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
};

export default TestInterface;