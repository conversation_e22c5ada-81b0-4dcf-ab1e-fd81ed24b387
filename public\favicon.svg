<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="gpuGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1d4ed8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chipGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- GPU Card Background -->
  <rect x="2" y="8" width="28" height="16" rx="2" ry="2" fill="url(#gpuGradient)" stroke="#1e40af" stroke-width="0.5"/>
  
  <!-- GPU Chip -->
  <rect x="6" y="12" width="8" height="8" rx="1" ry="1" fill="url(#chipGradient)" stroke="#047857" stroke-width="0.5"/>
  
  <!-- Circuit Lines -->
  <line x1="14" y1="14" x2="18" y2="14" stroke="#60a5fa" stroke-width="1" opacity="0.8"/>
  <line x1="14" y1="16" x2="20" y2="16" stroke="#60a5fa" stroke-width="1" opacity="0.8"/>
  <line x1="14" y1="18" x2="18" y2="18" stroke="#60a5fa" stroke-width="1" opacity="0.8"/>
  
  <!-- Heat Sink Fins -->
  <rect x="22" y="10" width="1" height="12" fill="#94a3b8" opacity="0.9"/>
  <rect x="24" y="11" width="1" height="10" fill="#94a3b8" opacity="0.9"/>
  <rect x="26" y="10" width="1" height="12" fill="#94a3b8" opacity="0.9"/>
  <rect x="28" y="11" width="1" height="10" fill="#94a3b8" opacity="0.9"/>
  
  <!-- Power Connectors -->
  <rect x="2" y="10" width="2" height="3" rx="0.5" ry="0.5" fill="#fbbf24"/>
  <rect x="2" y="19" width="2" height="3" rx="0.5" ry="0.5" fill="#fbbf24"/>
  
  <!-- GPU Chip Details -->
  <rect x="7" y="13" width="2" height="2" fill="#34d399" opacity="0.6"/>
  <rect x="10" y="13" width="2" height="2" fill="#34d399" opacity="0.6"/>
  <rect x="7" y="16" width="2" height="2" fill="#34d399" opacity="0.6"/>
  <rect x="10" y="16" width="2" height="2" fill="#34d399" opacity="0.6"/>
  
  <!-- Performance Indicator -->
  <circle cx="26" cy="6" r="2" fill="#ef4444" opacity="0.8">
    <animate attributeName="opacity" values="0.4;1;0.4" dur="2s" repeatCount="indefinite"/>
  </circle>
</svg>