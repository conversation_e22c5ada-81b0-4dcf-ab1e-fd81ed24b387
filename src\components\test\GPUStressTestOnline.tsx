import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Play, Square, RotateCcw, Thermometer, Cpu, HardDrive, Zap, Monitor, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface TestLevel {
  id: 'light' | 'medium' | 'heavy' | 'extreme';
  name: string;
  icon: string;
  objects: number;
  duration: number;
  description: string;
  baseFPS: number;
  tempStart: number;
}

interface TestMetrics {
  fps: number;
  avgFps: number;
  objects: number;
  temperature: number;
  timeRemaining: number;
  progress: number;
}

interface TestResults {
  level: string;
  avgFps: number;
  maxFps: number;
  minFps: number;
  maxTemp: number;
  score: number;
  grade: string;
  recommendation: string;
}

const GPUStressTestOnline: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<'home' | 'levels' | 'test' | 'results'>('home');
  const [selectedLevel, setSelectedLevel] = useState<TestLevel | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [metrics, setMetrics] = useState<TestMetrics>({
    fps: 0,
    avgFps: 0,
    objects: 0,
    temperature: 0,
    timeRemaining: 30,
    progress: 0
  });
  const [testResults, setTestResults] = useState<TestResults | null>(null);
  const [testSeconds, setTestSeconds] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  const testLevels: TestLevel[] = [
    {
      id: 'light',
      name: 'Light Test',
      icon: '🟢',
      objects: 50,
      duration: 30,
      description: 'Perfect for basic GPU testing and older hardware',
      baseFPS: 60,
      tempStart: 65
    },
    {
      id: 'medium',
      name: 'Medium Test',
      icon: '🟡',
      objects: 150,
      duration: 30,
      description: 'Standard stress test for modern GPUs',
      baseFPS: 45,
      tempStart: 70
    },
    {
      id: 'heavy',
      name: 'Heavy Test',
      icon: '🟠',
      objects: 300,
      duration: 30,
      description: 'Intensive testing for high-end graphics cards',
      baseFPS: 30,
      tempStart: 75
    },
    {
      id: 'extreme',
      name: 'Extreme Test',
      icon: '🔴',
      objects: 500,
      duration: 30,
      description: 'Maximum stress test for professional GPUs',
      baseFPS: 20,
      tempStart: 80
    }
  ];

  const startTest = (level: TestLevel) => {
    setSelectedLevel(level);
    setCurrentPage('test');
    setIsRunning(true);
    setTestSeconds(0);
    setMetrics({
      fps: level.baseFPS,
      avgFps: level.baseFPS,
      objects: level.objects,
      temperature: level.tempStart,
      timeRemaining: level.duration,
      progress: 0
    });

    toast({
      title: "Test Started",
      description: `Running ${level.name} for ${level.duration} seconds`,
    });

    // Start test timer
    timerRef.current = setInterval(() => {
      setTestSeconds(prev => {
        const newSeconds = prev + 1;
        const remaining = level.duration - newSeconds;
        const progress = (newSeconds / level.duration) * 100;
        
        // Simulate performance metrics
        const currentFPS = level.baseFPS + Math.floor(Math.random() * 20 - 10);
        const avgFPS = Math.floor(level.baseFPS + (Math.random() * 10 - 5));
        const temp = level.tempStart + Math.floor(newSeconds * 0.5) + Math.floor(Math.random() * 6 - 3);
        
        setMetrics({
          fps: Math.max(1, currentFPS),
          avgFps: Math.max(1, avgFPS),
          objects: level.objects,
          temperature: Math.min(95, Math.max(65, temp)),
          timeRemaining: Math.max(0, remaining),
          progress: Math.min(100, progress)
        });
        
        // Complete test
        if (newSeconds >= level.duration) {
          completeTest(level, avgFPS, currentFPS, temp);
          return newSeconds;
        }
        
        return newSeconds;
      });
    }, 1000);
  };

  const stopTest = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setIsRunning(false);
    if (selectedLevel) {
      completeTest(selectedLevel, metrics.avgFps, metrics.fps, metrics.temperature);
    }
  };

  const completeTest = (level: TestLevel, avgFps: number, maxFps: number, maxTemp: number) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setIsRunning(false);
    
    // Calculate score and grade
    const score = Math.floor((avgFps / level.baseFPS) * 1000);
    let grade = 'Poor';
    let recommendation = 'Consider upgrading your GPU for better performance.';
    
    if (score >= 1200) {
      grade = 'Excellent';
      recommendation = 'Outstanding performance! Your GPU handles this test with ease.';
    } else if (score >= 1000) {
      grade = 'Good';
      recommendation = 'Good performance. Your GPU is well-suited for this workload.';
    } else if (score >= 800) {
      grade = 'Fair';
      recommendation = 'Acceptable performance. Consider optimizing settings for better results.';
    }
    
    const results: TestResults = {
      level: level.name,
      avgFps: avgFps,
      maxFps: maxFps,
      minFps: Math.max(1, avgFps - 15),
      maxTemp: maxTemp,
      score: score,
      grade: grade,
      recommendation: recommendation
    };
    
    setTestResults(results);
    setCurrentPage('results');
    
    toast({
      title: "Test Completed",
      description: `Score: ${score} | Grade: ${grade}`,
    });
  };

  const resetTest = () => {
    setCurrentPage('home');
    setSelectedLevel(null);
    setTestResults(null);
    setIsRunning(false);
    setTestSeconds(0);
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const renderHomePage = () => (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-3xl font-bold gradient-text mb-4">
          GPU Stress Test Online
        </CardTitle>
        <CardDescription className="text-lg">
          Put your GPU to the ultimate test with our comprehensive stress testing suite. 
          Measure performance, monitor temperatures, and ensure your graphics card can handle 
          the most demanding workloads.
        </CardDescription>
      </CardHeader>
      <CardContent className="text-center">
        <Button 
          onClick={() => setCurrentPage('levels')} 
          size="lg" 
          className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90"
        >
          <Play className="w-5 h-5 mr-2" />
          Start GPU Test
        </Button>
      </CardContent>
    </Card>
  );

  const renderLevelSelection = () => (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-primary mb-2">
          Select Test Level
        </CardTitle>
        <CardDescription>
          Choose the intensity of your GPU stress test
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          {testLevels.map((level) => (
            <Card key={level.id} className="hover:shadow-lg transition-all duration-300 cursor-pointer group" onClick={() => startTest(level)}>
              <CardHeader className="text-center">
                <div className="text-4xl mb-2">{level.icon}</div>
                <CardTitle className="text-lg">{level.name}</CardTitle>
                <div className="flex justify-center space-x-4 text-sm text-muted-foreground">
                  <span>{level.objects} Objects</span>
                  <span>{level.duration} seconds</span>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-sm text-muted-foreground mb-4">{level.description}</p>
                <Button className="w-full group-hover:bg-primary/90">
                  Start {level.name}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="text-center">
          <Button variant="outline" onClick={() => setCurrentPage('home')}>
            Back to Home
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderTestRunning = () => (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-primary mb-4">
          Running {selectedLevel?.name}...
        </CardTitle>
        <div className="mb-6">
          <Progress value={metrics.progress} className="h-4 mb-2" />
          <p className="text-xl font-semibold text-primary">
            Time Remaining: {metrics.timeRemaining} seconds
          </p>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-primary">{metrics.fps}</div>
            <div className="text-sm text-muted-foreground">Current FPS</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-primary">{metrics.avgFps}</div>
            <div className="text-sm text-muted-foreground">Average FPS</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-primary">{metrics.objects}</div>
            <div className="text-sm text-muted-foreground">Objects</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-orange-500">{metrics.temperature}°C</div>
            <div className="text-sm text-muted-foreground">GPU Temp</div>
          </Card>
        </div>
        
        <div className="text-center space-y-4">
          <p className="text-muted-foreground">GPU stress test in progress...</p>
          <p className="text-sm text-muted-foreground">Rendering 3D objects and measuring performance</p>
          <Button variant="destructive" onClick={stopTest}>
            <Square className="w-4 h-4 mr-2" />
            Stop Test
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderResults = () => (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-primary mb-4">
          Test Results - {testResults?.level}
        </CardTitle>
        <div className="text-6xl font-bold mb-2">
          <span className="gradient-text">{testResults?.score}</span>
        </div>
        <Badge variant={testResults?.grade === 'Excellent' ? 'default' : testResults?.grade === 'Good' ? 'secondary' : 'destructive'} className="text-lg px-4 py-2">
          {testResults?.grade}
        </Badge>
      </CardHeader>
      <CardContent>
        <div className="grid md:grid-cols-3 gap-4 mb-6">
          <Card className="text-center p-4">
            <Cpu className="w-8 h-8 mx-auto mb-2 text-primary" />
            <div className="text-xl font-bold">{testResults?.avgFps}</div>
            <div className="text-sm text-muted-foreground">Average FPS</div>
          </Card>
          <Card className="text-center p-4">
            <Monitor className="w-8 h-8 mx-auto mb-2 text-primary" />
            <div className="text-xl font-bold">{testResults?.maxFps}</div>
            <div className="text-sm text-muted-foreground">Max FPS</div>
          </Card>
          <Card className="text-center p-4">
            <Thermometer className="w-8 h-8 mx-auto mb-2 text-orange-500" />
            <div className="text-xl font-bold">{testResults?.maxTemp}°C</div>
            <div className="text-sm text-muted-foreground">Max Temperature</div>
          </Card>
        </div>
        
        <Card className="p-4 mb-6">
          <h3 className="font-semibold mb-2">Recommendation</h3>
          <p className="text-muted-foreground">{testResults?.recommendation}</p>
        </Card>
        
        <div className="flex justify-center space-x-4">
          <Button onClick={() => setCurrentPage('levels')}>
            <RotateCcw className="w-4 h-4 mr-2" />
            Run Another Test
          </Button>
          <Button variant="outline" onClick={resetTest}>
            Back to Home
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="py-8">
      {currentPage === 'home' && renderHomePage()}
      {currentPage === 'levels' && renderLevelSelection()}
      {currentPage === 'test' && renderTestRunning()}
      {currentPage === 'results' && renderResults()}
    </div>
  );
};

export default GPUStressTestOnline;