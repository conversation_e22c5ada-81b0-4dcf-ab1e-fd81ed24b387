import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Play, Square, RotateCcw, Thermometer, Cpu, HardDrive, Zap, AlertTriangle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface TestMetrics {
  fps: number;
  avgFps: number;
  minFps: number;
  maxFps: number;
  frameTime: number;
  temperature: number;
  memoryUsage: number;
  gpuUtilization: number;
  powerDraw: number;
  score: number;
}

interface TestResults {
  duration: number;
  finalScore: number;
  avgFps: number;
  minFps: number;
  maxFps: number;
  temperatureMax: number;
  stability: 'Excellent' | 'Good' | 'Fair' | 'Poor';
  recommendation: string;
}

const GPUStressTest: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const glRef = useRef<WebGLRenderingContext | null>(null);
  const programRef = useRef<WebGLProgram | null>(null);
  const startTimeRef = useRef<number>(0);
  const frameCountRef = useRef<number>(0);
  const fpsHistoryRef = useRef<number[]>([]);
  
  const [isRunning, setIsRunning] = useState(false);
  const [testDuration, setTestDuration] = useState(0);
  const [selectedTest, setSelectedTest] = useState<'light' | 'medium' | 'heavy' | 'extreme'>('medium');
  const [metrics, setMetrics] = useState<TestMetrics>({
    fps: 0,
    avgFps: 0,
    minFps: Infinity,
    maxFps: 0,
    frameTime: 0,
    temperature: 35,
    memoryUsage: 15,
    gpuUtilization: 0,
    powerDraw: 50,
    score: 0
  });
  const [testResults, setTestResults] = useState<TestResults | null>(null);
  const [progress, setProgress] = useState(0);
  
  const { toast } = useToast();

  const testConfigs = {
    light: {
      name: 'Light Test',
      description: 'Basic GPU testing with simple geometry',
      duration: 30,
      complexity: 1000,
      particles: 500
    },
    medium: {
      name: 'Medium Test', 
      description: 'Standard stress testing with moderate complexity',
      duration: 60,
      complexity: 5000,
      particles: 2000
    },
    heavy: {
      name: 'Heavy Test',
      description: 'Intensive testing with complex shaders and geometry',
      duration: 120,
      complexity: 15000,
      particles: 5000
    },
    extreme: {
      name: 'Extreme Test',
      description: 'Maximum stress testing for high-end GPUs',
      duration: 300,
      complexity: 50000,
      particles: 15000
    }
  };

  const initWebGL = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return false;

    // Set canvas size for better mobile responsiveness
    const rect = canvas.getBoundingClientRect();
    canvas.width = Math.min(800, rect.width || 800);
    canvas.height = Math.min(600, (rect.width || 800) * 0.75);

    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext | null;
    if (!gl) {
      toast({
        title: "WebGL Not Supported",
        description: "Your browser doesn't support WebGL. Please use a modern browser.",
        variant: "destructive"
      });
      return false;
    }

    glRef.current = gl;

    // Vertex shader source
    const vertexShaderSource = `
      precision mediump float;
      attribute vec2 a_position;
      attribute vec3 a_color;
      varying vec3 v_color;
      uniform vec2 u_resolution;
      uniform float u_time;
      
      void main() {
        vec2 position = a_position;
        position.x += sin(u_time * 0.001 + a_position.y * 0.01) * 0.1;
        position.y += cos(u_time * 0.002 + a_position.x * 0.01) * 0.1;
        
        vec2 zeroToOne = position / u_resolution;
        vec2 zeroToTwo = zeroToOne * 2.0;
        vec2 clipSpace = zeroToTwo - 1.0;
        
        gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);
        v_color = a_color;
      }
    `;

    // Fragment shader source
    const fragmentShaderSource = `
      precision mediump float;
      varying vec3 v_color;
      uniform float u_time;
      
      void main() {
        vec3 color = v_color;
        color.r += sin(u_time * 0.003) * 0.3;
        color.g += cos(u_time * 0.004) * 0.3;
        color.b += sin(u_time * 0.005) * 0.3;
        gl_FragColor = vec4(color, 1.0);
      }
    `;

    // Create shader function
    const createShader = (gl: WebGLRenderingContext, type: number, source: string): WebGLShader | null => {
      const shader = gl.createShader(type);
      if (!shader) return null;
      
      gl.shaderSource(shader, source);
      gl.compileShader(shader);
      
      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('Shader compile error:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
      }
      
      return shader;
    };

    // Create program
    const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
    
    if (!vertexShader || !fragmentShader) return false;

    const program = gl.createProgram();
    if (!program) return false;

    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('Program link error:', gl.getProgramInfoLog(program));
      return false;
    }

    programRef.current = program;
    return true;
  }, [toast]);

  const generateGeometry = useCallback((complexity: number) => {
    const positions: number[] = [];
    const colors: number[] = [];

    for (let i = 0; i < complexity; i++) {
      // Triangle positions
      const x = Math.random() * 800;
      const y = Math.random() * 600;
      const size = Math.random() * 20 + 5;

      positions.push(
        x, y,
        x + size, y,
        x + size/2, y + size
      );

      // Colors
      const r = Math.random();
      const g = Math.random();
      const b = Math.random();
      
      colors.push(r, g, b, r, g, b, r, g, b);
    }

    return { positions, colors };
  }, []);

  const updateMetrics = useCallback((deltaTime: number, frameTime: number) => {
    const currentFps = 1000 / frameTime;
    fpsHistoryRef.current.push(currentFps);
    
    if (fpsHistoryRef.current.length > 60) {
      fpsHistoryRef.current.shift();
    }

    const avgFps = fpsHistoryRef.current.reduce((a, b) => a + b, 0) / fpsHistoryRef.current.length;
    const minFps = Math.min(...fpsHistoryRef.current);
    const maxFps = Math.max(...fpsHistoryRef.current);

    // Simulate realistic metrics based on performance
    const performanceRatio = avgFps / 60; // Assuming 60fps as baseline
    const baseTemp = 35;
    const tempIncrease = (1 - performanceRatio) * 30 + (testDuration / 60) * 10;
    const temperature = Math.min(baseTemp + tempIncrease, 95);
    
    const memoryUsage = Math.min(15 + (testDuration / 10) * 2 + (1 - performanceRatio) * 20, 95);
    const gpuUtilization = Math.min(20 + performanceRatio * 70 + Math.random() * 10, 100);
    const powerDraw = Math.min(50 + gpuUtilization * 2 + temperature * 0.5, 350);
    
    // Calculate score based on performance stability
    const fpsStability = 1 - (maxFps - minFps) / avgFps;
    const tempScore = Math.max(0, (85 - temperature) / 85);
    const fpsScore = Math.min(avgFps / 60, 2);
    const score = (fpsStability * 0.3 + tempScore * 0.3 + fpsScore * 0.4) * 100;

    setMetrics({
      fps: Math.round(currentFps),
      avgFps: Math.round(avgFps),
      minFps: Math.round(minFps),
      maxFps: Math.round(maxFps),
      frameTime: Math.round(frameTime * 100) / 100,
      temperature: Math.round(temperature),
      memoryUsage: Math.round(memoryUsage),
      gpuUtilization: Math.round(gpuUtilization),
      powerDraw: Math.round(powerDraw),
      score: Math.round(score)
    });
  }, [testDuration]);

  const stopTest = useCallback(() => {
    setIsRunning(false);
    
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    // Calculate final results
    const finalResults: TestResults = {
      duration: testDuration,
      finalScore: metrics.score,
      avgFps: metrics.avgFps,
      minFps: metrics.minFps,
      maxFps: metrics.maxFps,
      temperatureMax: metrics.temperature,
      stability: metrics.score >= 80 ? 'Excellent' : 
                metrics.score >= 60 ? 'Good' : 
                metrics.score >= 40 ? 'Fair' : 'Poor',
      recommendation: metrics.score >= 80 ? 'Your GPU performs excellently and can handle demanding tasks.' :
                     metrics.score >= 60 ? 'Good performance, suitable for most gaming and professional work.' :
                     metrics.score >= 40 ? 'Average performance, consider upgrading for demanding applications.' :
                     'Poor performance detected, GPU may need replacement or system optimization.'
    };

    setTestResults(finalResults);
    
    toast({
      title: "Test Completed",
      description: `Final score: ${finalResults.finalScore}/100 (${finalResults.stability})`,
    });
  }, [testDuration, metrics, toast]);

  const render = useCallback((timestamp: number) => {
    if (!glRef.current || !programRef.current || !canvasRef.current) return;

    const gl = glRef.current;
    const program = programRef.current;
    const config = testConfigs[selectedTest];

    if (frameCountRef.current === 0) {
      startTimeRef.current = timestamp;
    }

    const deltaTime = timestamp - startTimeRef.current;
    const frameTime = deltaTime / frameCountRef.current || 16.67;
    
    frameCountRef.current++;

    // Update metrics
    updateMetrics(deltaTime, frameTime);

    // Render
    gl.viewport(0, 0, canvasRef.current.width, canvasRef.current.height);
    gl.clearColor(0.0, 0.0, 0.1, 1.0);
    gl.clear(gl.COLOR_BUFFER_BIT);

    gl.useProgram(program);

    // Generate and render geometry
    const { positions, colors } = generateGeometry(config.complexity);

    // Position buffer
    const positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);

    const positionLocation = gl.getAttribLocation(program, 'a_position');
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

    // Color buffer
    const colorBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(colors), gl.STATIC_DRAW);

    const colorLocation = gl.getAttribLocation(program, 'a_color');
    gl.enableVertexAttribArray(colorLocation);
    gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 0, 0);

    // Uniforms
    const resolutionLocation = gl.getUniformLocation(program, 'u_resolution');
    gl.uniform2f(resolutionLocation, canvasRef.current.width, canvasRef.current.height);

    const timeLocation = gl.getUniformLocation(program, 'u_time');
    gl.uniform1f(timeLocation, timestamp);

    // Draw
    gl.drawArrays(gl.TRIANGLES, 0, positions.length / 2);

    if (isRunning) {
      animationRef.current = requestAnimationFrame(render);
    }
  }, [selectedTest, isRunning, updateMetrics, stopTest]);

  const startTest = useCallback(() => {
    if (!initWebGL()) return;

    setIsRunning(true);
    setTestDuration(0);
    setProgress(0);
    setTestResults(null);
    frameCountRef.current = 0;
    fpsHistoryRef.current = [];
    
    setMetrics({
      fps: 0,
      avgFps: 0,
      minFps: Infinity,
      maxFps: 0,
      frameTime: 0,
      temperature: 35,
      memoryUsage: 15,
      gpuUtilization: 0,
      powerDraw: 50,
      score: 0
    });

    animationRef.current = requestAnimationFrame(render);
    
    toast({
      title: "GPU Stress Test Started",
      description: `Running ${testConfigs[selectedTest].name} for ${testConfigs[selectedTest].duration} seconds`,
    });
  }, [initWebGL, render, selectedTest, toast]);

  const resetTest = useCallback(() => {
    setIsRunning(false);
    setTestDuration(0);
    setProgress(0);
    setTestResults(null);
    frameCountRef.current = 0;
    fpsHistoryRef.current = [];
    
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    setMetrics({
      fps: 0,
      avgFps: 0,
      minFps: Infinity,
      maxFps: 0,
      frameTime: 0,
      temperature: 35,
      memoryUsage: 15,
      gpuUtilization: 0,
      powerDraw: 50,
      score: 0
    });

    // Clear canvas
    if (canvasRef.current && glRef.current) {
      const gl = glRef.current;
      gl.clearColor(0.0, 0.0, 0.1, 1.0);
      gl.clear(gl.COLOR_BUFFER_BIT);
    }
  }, []);

  // Timer effect for updating test progress
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    
    if (isRunning) {
      const testConfig = testConfigs[selectedTest];
      const startTime = Date.now();
      
      intervalId = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const elapsedSeconds = Math.floor(elapsed / 1000);
        const progressPercent = Math.min((elapsed / (testConfig.duration * 1000)) * 100, 100);
        
        setTestDuration(elapsedSeconds);
        setProgress(progressPercent);
        
        // Auto-stop test when duration is reached
        if (elapsedSeconds >= testConfig.duration) {
          stopTest();
        }
      }, 100); // Update every 100ms for smooth progress
    }
    
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isRunning, selectedTest, stopTest]);

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const getStatusColor = (value: number, thresholds: [number, number, number]) => {
    if (value <= thresholds[0]) return 'text-green-500';
    if (value <= thresholds[1]) return 'text-yellow-500';
    if (value <= thresholds[2]) return 'text-orange-500';
    return 'text-red-500';
  };

  const getStatusIcon = (stability: string) => {
    switch (stability) {
      case 'Excellent':
      case 'Good':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'Fair':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
    }
  };

  return (
    <div className="space-y-8">
      {/* Test Configuration */}
      <Card variant="tech">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-6 h-6 text-primary" />
            <span>GPU Stress Test Configuration</span>
          </CardTitle>
          <CardDescription>
            Select your test intensity level and monitor real-time GPU performance metrics
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Selection */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(testConfigs).map(([key, config]) => (
              <button
                key={key}
                onClick={() => setSelectedTest(key as any)}
                disabled={isRunning}
                className={`p-4 rounded-lg border-2 transition-all ${
                  selectedTest === key
                    ? 'border-primary bg-primary/10 shadow-glow-primary'
                    : 'border-muted bg-card hover:border-primary/50'
                } ${isRunning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              >
                <h4 className="font-semibold text-sm">{config.name}</h4>
                <p className="text-xs text-muted-foreground mt-1">{config.duration}s test</p>
                <div className="flex justify-center mt-2">
                  <div className={`w-2 h-2 rounded-full ${
                    key === 'light' ? 'bg-green-500' :
                    key === 'medium' ? 'bg-yellow-500' :
                    key === 'heavy' ? 'bg-orange-500' : 'bg-red-500'
                  }`} />
                </div>
              </button>
            ))}
          </div>

          {/* Test Controls */}
          <div className="space-y-4">
            {/* Mobile Layout */}
            <div className="md:hidden space-y-3">
              <Button
                onClick={startTest}
                disabled={isRunning}
                variant="hero"
                size="lg"
                className="w-full"
              >
                <Play className="w-5 h-5 mr-2" />
                Start Test
              </Button>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={stopTest}
                  disabled={!isRunning}
                  variant="outline"
                  size="lg"
                  className="w-full"
                >
                  <Square className="w-4 h-4 mr-1" />
                  Stop
                </Button>
                <Button
                  onClick={resetTest}
                  disabled={isRunning}
                  variant="outline"
                  size="lg"
                  className="w-full"
                >
                  <RotateCcw className="w-4 h-4 mr-1" />
                  Reset
                </Button>
              </div>
            </div>
            
            {/* Desktop Layout */}
            <div className="hidden md:flex justify-center space-x-4">
              <Button
                onClick={startTest}
                disabled={isRunning}
                variant="hero"
                size="lg"
              >
                <Play className="w-5 h-5 mr-2" />
                Start Test
              </Button>
              <Button
                onClick={stopTest}
                disabled={!isRunning}
                variant="outline"
                size="lg"
              >
                <Square className="w-5 h-5 mr-2" />
                Stop
              </Button>
              <Button
                onClick={resetTest}
                disabled={isRunning}
                variant="outline"
                size="lg"
              >
                <RotateCcw className="w-5 h-5 mr-2" />
                Reset
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          {(isRunning || testResults) && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Test Progress</span>
                <span>{Math.round(testDuration)}s / {testConfigs[selectedTest].duration}s</span>
              </div>
              <Progress value={progress} className="h-3" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Canvas */}
      <Card variant="tech">
        <CardHeader>
          <CardTitle>Real-time GPU Test Visualization</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative bg-slate-900 rounded-lg overflow-hidden">
            <canvas
              ref={canvasRef}
              width={800}
              height={600}
              className="w-full h-auto max-h-96 border border-primary/20"
              style={{ background: 'linear-gradient(45deg, #0f0f23, #1a1a2e)' }}
            />
            {!isRunning && !testResults && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                <div className="text-center">
                  <Zap className="w-16 h-16 text-primary mx-auto mb-4" />
                  <p className="text-white text-lg font-semibold">GPU Test Ready</p>
                  <p className="text-white/70">Click "Start Test" to begin</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Metrics */}
      {(isRunning || testResults) && (
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Performance Metrics */}
          <Card variant="glow">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Cpu className="w-5 h-5 mr-2 text-primary" />
                Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Current FPS:</span>
                <span className="font-semibold">{metrics.fps}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Average FPS:</span>
                <span className="font-semibold">{metrics.avgFps}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Min/Max FPS:</span>
                <span className="font-semibold">{metrics.minFps}/{metrics.maxFps}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Frame Time:</span>
                <span className="font-semibold">{metrics.frameTime}ms</span>
              </div>
            </CardContent>
          </Card>

          {/* Temperature */}
          <Card variant="glow">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Thermometer className="w-5 h-5 mr-2 text-orange-500" />
                Temperature
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-center">
                <div className={`text-3xl font-bold ${getStatusColor(metrics.temperature, [70, 80, 90])}`}>
                  {metrics.temperature}°C
                </div>
                <Progress value={(metrics.temperature / 100) * 100} className="mt-2" />
              </div>
              <div className="text-xs text-center text-muted-foreground">
                Safe: &lt;70°C | Warning: 70-80°C | Critical: &gt;80°C
              </div>
            </CardContent>
          </Card>

          {/* Memory & Utilization */}
          <Card variant="glow">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <HardDrive className="w-5 h-5 mr-2 text-blue-500" />
                Memory
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">GPU Usage:</span>
                <span className="font-semibold">{metrics.gpuUtilization}%</span>
              </div>
              <Progress value={metrics.gpuUtilization} className="h-2" />
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">VRAM Usage:</span>
                <span className="font-semibold">{metrics.memoryUsage}%</span>
              </div>
              <Progress value={metrics.memoryUsage} className="h-2" />
            </CardContent>
          </Card>

          {/* Score & Power */}
          <Card variant="glow">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Zap className="w-5 h-5 mr-2 text-yellow-500" />
                Score & Power
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {metrics.score}/100
                </div>
                <div className="text-sm text-muted-foreground">Performance Score</div>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Power Draw:</span>
                <span className="font-semibold">{metrics.powerDraw}W</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Test Results */}
      {testResults && (
        <Card variant="tech" className="border-2 border-primary shadow-glow-primary">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {getStatusIcon(testResults.stability)}
              <span>Test Results</span>
              <Badge variant="outline" className="ml-auto">
                {testResults.stability}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-gradient-card rounded-lg">
                <div className="text-3xl font-bold text-primary mb-2">
                  {testResults.finalScore}/100
                </div>
                <div className="text-sm text-muted-foreground">Final Score</div>
              </div>
              <div className="text-center p-4 bg-gradient-card rounded-lg">
                <div className="text-2xl font-bold text-foreground mb-2">
                  {testResults.avgFps} FPS
                </div>
                <div className="text-sm text-muted-foreground">Average FPS</div>
              </div>
              <div className="text-center p-4 bg-gradient-card rounded-lg">
                <div className="text-2xl font-bold text-foreground mb-2">
                  {testResults.temperatureMax}°C
                </div>
                <div className="text-sm text-muted-foreground">Max Temperature</div>
              </div>
            </div>
            
            <div className="p-4 bg-muted/20 rounded-lg">
              <h4 className="font-semibold mb-2">Recommendation:</h4>
              <p className="text-muted-foreground">{testResults.recommendation}</p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Test Duration:</span>
                  <span className="font-medium">{Math.round(testResults.duration)}s</span>
                </div>
                <div className="flex justify-between">
                  <span>Min FPS:</span>
                  <span className="font-medium">{testResults.minFps}</span>
                </div>
                <div className="flex justify-between">
                  <span>Max FPS:</span>
                  <span className="font-medium">{testResults.maxFps}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Stability Rating:</span>
                  <span className="font-medium">{testResults.stability}</span>
                </div>
                <div className="flex justify-between">
                  <span>Test Type:</span>
                  <span className="font-medium">{testConfigs[selectedTest].name}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default GPUStressTest;