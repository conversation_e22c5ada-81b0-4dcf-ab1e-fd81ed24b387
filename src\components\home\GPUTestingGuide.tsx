import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Thermometer, Activity, Clock, Shield, AlertTriangle, CheckCircle, TrendingUp, Monitor } from "lucide-react";
import { useNavigate } from "react-router-dom";
import gpuMonitoring from "@/assets/gpu-monitoring.jpg";
import stressTest from "@/assets/stress-test.jpg";

const GPUTestingGuide = () => {
  const navigate = useNavigate();

  const handleTestClick = () => {
    navigate('/');
    // 延迟滚动到测试区域，确保页面已加载
    setTimeout(() => {
      const testSection = document.querySelector('[data-testid="test-section"]');
      if (testSection) {
        testSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };
  const testingSteps = [
    {
      icon: Monitor,
      title: "GPU Detection",
      description: "Automatic detection of your graphics card model, VRAM, and driver version for accurate testing parameters."
    },
    {
      icon: Thermometer,
      title: "Temperature Monitoring",
      description: "Real-time temperature tracking to ensure your GPU operates within safe thermal limits during stress testing."
    },
    {
      icon: Activity,
      title: "Performance Analysis",
      description: "Comprehensive performance metrics including frame rates, memory usage, and computational throughput analysis."
    },
    {
      icon: Shield,
      title: "Stability Verification",
      description: "Advanced stability testing to identify potential issues, artifacts, or instabilities in your graphics card."
    }
  ];

  const safetyTips = [
    {
      icon: AlertTriangle,
      title: "Temperature Limits",
      description: "Monitor GPU temperatures closely. Most modern GPUs should stay under 85°C during stress testing. If temperatures exceed safe limits, stop testing immediately."
    },
    {
      icon: CheckCircle,
      title: "Proper Ventilation",
      description: "Ensure adequate case ventilation and clean GPU fans before testing. Poor airflow can lead to overheating and hardware damage."
    },
    {
      icon: Clock,
      title: "Testing Duration",
      description: "Start with short 5-10 minute tests. Gradually increase duration only if temperatures remain stable and within safe ranges."
    }
  ];

  return (
    <section className="py-20" id="guide">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-card border border-primary/20 rounded-full text-sm font-medium shadow-glow-primary mb-6">
            <TrendingUp className="w-4 h-4 mr-2 text-primary" />
            Professional GPU Testing Guide
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="gradient-text">Complete GPU Stress Testing</span> Guide
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Learn how to properly stress test your graphics card with our comprehensive free online GPU testing suite. 
            Understand the importance of GPU stress testing for gaming, professional workloads, and system stability.
          </p>
        </div>

        {/* What is GPU Stress Testing */}
        <div className="mb-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold mb-6">What is GPU Stress Testing?</h3>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  GPU stress testing is a critical process that pushes your graphics card to its maximum performance limits 
                  to evaluate stability, thermal performance, and overall reliability. Our free online GPU stress test tool 
                  provides comprehensive analysis without requiring software installation.
                </p>
                <p>
                  Professional GPU stress testing involves running intensive computational workloads that fully utilize your 
                  graphics card's processing units, memory subsystem, and power delivery components. This process helps identify 
                  potential hardware issues, thermal throttling, and performance bottlenecks.
                </p>
                <p>
                  Whether you're a gamer looking to optimize performance, a professional working with GPU-accelerated applications, 
                  or a system builder validating hardware stability, our free online GPU benchmark tool provides the insights you need.
                </p>
              </div>
            </div>
            <div className="relative">
              <img 
                src={gpuMonitoring} 
                alt="GPU performance monitoring dashboard showing real-time temperature, utilization, and benchmark results" 
                className="rounded-lg shadow-tech border border-primary/20"
              />
              <div className="absolute inset-0 bg-gradient-primary/10 rounded-lg"></div>
            </div>
          </div>
        </div>

        {/* Testing Process */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center mb-12">
            Professional GPU Testing Process
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {testingSteps.map((step, index) => (
              <Card key={index} variant="tech" className="text-center group hover:shadow-glow-primary transition-all duration-300">
                <CardHeader>
                  <div className="mx-auto p-3 bg-gradient-primary rounded-full w-fit shadow-glow-primary group-hover:shadow-glow-secondary transition-all duration-300">
                    <step.icon className="h-6 w-6 text-primary-foreground" />
                  </div>
                  <CardTitle className="text-lg">{step.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm leading-relaxed">
                    {step.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Why Stress Test Your GPU */}
        <div className="mb-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="relative order-2 lg:order-1">
              <img 
                src={stressTest} 
                alt="GPU stress testing interface with temperature monitoring, performance metrics, and stability analysis" 
                className="rounded-lg shadow-tech border border-primary/20"
              />
              <div className="absolute inset-0 bg-gradient-secondary/10 rounded-lg"></div>
            </div>
            <div className="order-1 lg:order-2">
              <h3 className="text-2xl font-bold mb-6">Why Stress Test Your GPU?</h3>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  <strong className="text-foreground">Gaming Performance Optimization:</strong> Identify the maximum stable 
                  overclock settings for your graphics card to achieve higher frame rates in demanding games. Our free GPU 
                  stress test helps you find the perfect balance between performance and stability.
                </p>
                <p>
                  <strong className="text-foreground">Hardware Validation:</strong> Verify that your new graphics card 
                  functions correctly and can handle intensive workloads without crashes, artifacts, or thermal issues. 
                  This is essential for new system builds and hardware upgrades.
                </p>
                <p>
                  <strong className="text-foreground">Professional Workloads:</strong> Ensure your GPU can reliably handle 
                  professional applications like 3D rendering, video editing, machine learning, and cryptocurrency mining 
                  without stability issues or performance degradation.
                </p>
                <p>
                  <strong className="text-foreground">Thermal Management:</strong> Monitor GPU temperatures under maximum 
                  load to verify that your cooling solution is adequate and identify potential thermal throttling issues 
                  that could impact performance.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Safety Guidelines */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center mb-12">
            GPU Stress Testing Safety Guidelines
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            {safetyTips.map((tip, index) => (
              <Card key={index} variant="glow" className="group hover:shadow-glow-accent transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-secondary rounded-lg shadow-glow-secondary">
                      <tip.icon className="h-5 w-5 text-secondary-foreground" />
                    </div>
                    <CardTitle className="text-lg">{tip.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="leading-relaxed">
                    {tip.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* GPU Testing Best Practices */}
        <div className="mb-16">
          <Card variant="tech" className="p-8">
            <h3 className="text-2xl font-bold mb-6">GPU Testing Best Practices</h3>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="text-lg font-semibold mb-4 text-primary">Before Testing</h4>
                <ul className="space-y-2 text-muted-foreground">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                    <span>Update GPU drivers to the latest version for optimal performance and compatibility</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                    <span>Close unnecessary applications to ensure accurate testing results</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                    <span>Verify adequate power supply capacity for your graphics card</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                    <span>Clean GPU fans and ensure proper case ventilation</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-lg font-semibold mb-4 text-primary">During Testing</h4>
                <ul className="space-y-2 text-muted-foreground">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                    <span>Monitor temperatures continuously during stress testing</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                    <span>Watch for visual artifacts or rendering anomalies</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                    <span>Start with shorter test durations and gradually increase</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-accent mt-0.5 flex-shrink-0" />
                    <span>Stop testing immediately if temperatures exceed safe limits</span>
                  </li>
                </ul>
              </div>
            </div>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="inline-flex flex-col items-center space-y-4 p-8 bg-gradient-card border border-primary/20 rounded-lg shadow-glow-primary">
            <h3 className="text-2xl font-bold gradient-text">Ready to Test Your GPU?</h3>
            <p className="text-muted-foreground max-w-2xl">
              Start your free online GPU stress test now and discover your graphics card's true performance potential. 
              Our comprehensive testing suite provides professional-grade analysis without any software installation.
            </p>
            <Button variant="hero" size="xl" className="mt-4" onClick={handleTestClick}>
              <Activity className="w-5 h-5 mr-2" />
              Begin Free GPU Stress Test
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GPUTestingGuide;