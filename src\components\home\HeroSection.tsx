import { <PERSON><PERSON> } from "@/components/ui/button";
import { Play, Download, BarChart3 } from "lucide-react";
import heroImage from "@/assets/gpu-hero.jpg";

const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <img 
          src={heroImage} 
          alt="GPU stress testing visualization with modern graphics card" 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/60"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20"></div>
      </div>
      
      {/* Animated Background Elements */}
      <div className="absolute inset-0 z-10">
        <div className="absolute top-20 left-10 w-4 h-4 bg-primary rounded-full animate-tech-pulse"></div>
        <div className="absolute top-40 right-20 w-3 h-3 bg-secondary rounded-full animate-float"></div>
        <div className="absolute bottom-40 left-20 w-5 h-5 bg-accent rounded-full animate-tech-pulse"></div>
        <div className="absolute bottom-20 right-10 w-2 h-2 bg-primary-glow rounded-full animate-float"></div>
      </div>

      {/* Hero Content */}
      <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="space-y-8">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 bg-gradient-card border border-primary/20 rounded-full text-sm font-medium shadow-glow-primary">
            <BarChart3 className="w-4 h-4 mr-2 text-primary" />
            Free Online GPU Stress Test Tool
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
            <span className="gradient-text">Ultimate GPU</span>
            <br />
            <span className="text-foreground">Stress Test</span>
          </h1>

          {/* Subheading */}
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Professional free online graphics card testing tool. Benchmark your GPU performance, 
            monitor temperatures, and ensure optimal stability with our comprehensive stress testing suite.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
            <Button 
              variant="hero" 
              size="xl" 
              className="group"
              onClick={() => {
                const testSection = document.getElementById('test');
                testSection?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
              Start Free GPU Test
            </Button>
            <Button 
              variant="tech" 
              size="xl"
              onClick={() => {
                const testSection = document.getElementById('test');
                testSection?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              <Download className="w-5 h-5 mr-2" />
              View Test Results
            </Button>
          </div>

          {/* Features Pills */}
          <div className="flex flex-wrap justify-center gap-3 pt-8">
            {[
              "Real-time Monitoring",
              "Temperature Tracking", 
              "Performance Analysis",
              "Stability Testing",
              "Benchmark Comparison"
            ].map((feature) => (
              <div 
                key={feature}
                className="px-4 py-2 bg-card/30 backdrop-blur-sm border border-border/50 rounded-full text-sm font-medium hover:bg-card/50 transition-all duration-300"
              >
                {feature}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="w-6 h-10 border-2 border-primary/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;