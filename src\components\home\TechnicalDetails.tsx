import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Settings, Code, Database, Shield, Cpu, MemoryStick, Thermometer, Activity } from "lucide-react";

const TechnicalDetails = () => {
  const technicalSpecs = [
    {
      category: "Graphics APIs",
      items: ["WebGL 2.0", "WebGPU", "OpenGL ES 3.0", "DirectX 11", "Vulkan Detection"]
    },
    {
      category: "Test Parameters",
      items: ["Custom Resolution", "Variable Load Intensity", "Texture Complexity", "Shader Programs", "Particle Systems"]
    },
    {
      category: "Monitoring Metrics",
      items: ["GPU Temperature", "VRAM Usage", "Power Consumption", "Clock Speeds", "Thermal Throttling"]
    },
    {
      category: "Compatibility",
      items: ["Chrome 90+", "Firefox 88+", "Safari 14+", "Edge 90+", "Mobile Browsers"]
    }
  ];

  const testingMethodology = [
    {
      icon: Settings,
      title: "Adaptive Load Scaling",
      description: "Our free online GPU stress test automatically adjusts workload intensity based on your hardware capabilities, ensuring optimal testing conditions while preventing thermal damage."
    },
    {
      icon: Code,
      title: "Advanced Shader Testing",
      description: "Complex fragment and vertex shaders stress test computational units, memory bandwidth, and rendering pipelines to identify performance characteristics and stability issues."
    },
    {
      icon: Database,
      title: "Memory Pattern Analysis",
      description: "Comprehensive VRAM testing with various access patterns, texture sizes, and buffer allocations to evaluate memory subsystem performance and detect potential issues."
    },
    {
      icon: Shield,
      title: "Safety Monitoring",
      description: "Real-time temperature and performance monitoring with automatic test termination if dangerous thermal conditions or instability is detected during stress testing."
    }
  ];

  const hardwareCompatibility = [
    {
      manufacturer: "NVIDIA",
      series: ["RTX 40 Series", "RTX 30 Series", "RTX 20 Series", "GTX 16 Series", "GTX 10 Series"],
      features: ["CUDA Cores", "RT Cores", "Tensor Cores", "DLSS Support", "Ray Tracing"]
    },
    {
      manufacturer: "AMD",
      series: ["RX 7000 Series", "RX 6000 Series", "RX 5000 Series", "RX 500 Series", "RX 400 Series"],
      features: ["Stream Processors", "Infinity Cache", "RDNA Architecture", "FSR Support", "Hardware Acceleration"]
    },
    {
      manufacturer: "Intel",
      series: ["Arc A-Series", "Iris Xe Graphics", "UHD Graphics", "HD Graphics"],
      features: ["Xe Cores", "AV1 Encoding", "XeSS Support", "Variable Rate Shading", "Mesh Shaders"]
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-card/20 to-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-card border border-primary/20 rounded-full text-sm font-medium shadow-glow-primary mb-6">
            <Settings className="w-4 h-4 mr-2 text-primary" />
            Technical Specifications
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="gradient-text">Advanced GPU Testing</span> Technology
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Our free online GPU stress test utilizes cutting-edge web technologies and advanced testing methodologies 
            to provide professional-grade graphics card analysis without software installation.
          </p>
        </div>

        {/* Technical Specifications */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center mb-8">Technical Specifications</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {technicalSpecs.map((spec, index) => (
              <Card key={index} variant="tech" className="group hover:shadow-glow-primary transition-all duration-300">
                <CardHeader>
                  <CardTitle className="text-lg text-center">{spec.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {spec.items.map((item, itemIndex) => (
                      <Badge key={itemIndex} variant="secondary" className="text-xs w-full justify-center">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Testing Methodology */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center mb-12">
            Advanced Testing Methodology
          </h3>
          <div className="grid md:grid-cols-2 gap-8">
            {testingMethodology.map((method, index) => (
              <Card key={index} variant="glow" className="group hover:shadow-glow-secondary transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-gradient-primary rounded-lg shadow-glow-primary group-hover:shadow-glow-accent transition-all duration-300">
                      <method.icon className="h-6 w-6 text-primary-foreground" />
                    </div>
                    <CardTitle className="text-xl">{method.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {method.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Hardware Compatibility */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center mb-8">GPU Hardware Compatibility</h3>
          <Tabs defaultValue="nvidia" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8">
              <TabsTrigger value="nvidia">NVIDIA Graphics</TabsTrigger>
              <TabsTrigger value="amd">AMD Graphics</TabsTrigger>
              <TabsTrigger value="intel">Intel Graphics</TabsTrigger>
            </TabsList>
            
            {hardwareCompatibility.map((brand) => (
              <TabsContent key={brand.manufacturer.toLowerCase()} value={brand.manufacturer.toLowerCase()}>
                <Card variant="tech" className="p-6">
                  <div className="grid lg:grid-cols-2 gap-8">
                    <div>
                      <h4 className="text-lg font-semibold mb-4 text-primary">
                        Supported {brand.manufacturer} GPU Series
                      </h4>
                      <div className="space-y-2">
                        {brand.series.map((series, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-accent rounded-full"></div>
                            <span className="text-muted-foreground">{series}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold mb-4 text-primary">
                        Tested Features & Technologies
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {brand.features.map((feature, index) => (
                          <Badge key={index} variant="outline" className="border-primary/30">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </div>

        {/* Performance Metrics */}
        <div className="mb-16">
          <Card variant="tech" className="p-8">
            <h3 className="text-2xl font-bold text-center mb-8">Real-time Performance Metrics</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center space-y-3">
                <div className="p-4 bg-gradient-primary rounded-full w-fit mx-auto shadow-glow-primary">
                  <Cpu className="h-8 w-8 text-primary-foreground" />
                </div>
                <h4 className="font-semibold">GPU Utilization</h4>
                <p className="text-sm text-muted-foreground">Real-time GPU core usage percentage and load distribution across compute units</p>
              </div>
              <div className="text-center space-y-3">
                <div className="p-4 bg-gradient-secondary rounded-full w-fit mx-auto shadow-glow-secondary">
                  <MemoryStick className="h-8 w-8 text-secondary-foreground" />
                </div>
                <h4 className="font-semibold">VRAM Usage</h4>
                <p className="text-sm text-muted-foreground">Video memory allocation, bandwidth utilization, and available capacity monitoring</p>
              </div>
              <div className="text-center space-y-3">
                <div className="p-4 bg-gradient-primary rounded-full w-fit mx-auto shadow-glow-primary">
                  <Thermometer className="h-8 w-8 text-primary-foreground" />
                </div>
                <h4 className="font-semibold">Temperature</h4>
                <p className="text-sm text-muted-foreground">GPU core temperature monitoring with thermal throttling detection and alerts</p>
              </div>
              <div className="text-center space-y-3">
                <div className="p-4 bg-gradient-secondary rounded-full w-fit mx-auto shadow-glow-secondary">
                  <Activity className="h-8 w-8 text-secondary-foreground" />
                </div>
                <h4 className="font-semibold">Performance Score</h4>
                <p className="text-sm text-muted-foreground">Comprehensive performance rating based on multiple benchmark criteria and stability</p>
              </div>
            </div>
          </Card>
        </div>

        {/* System Requirements */}
        <div className="text-center">
          <Card variant="glow" className="p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold mb-6 gradient-text">Minimum System Requirements</h3>
            <div className="grid md:grid-cols-2 gap-8 text-left">
              <div>
                <h4 className="font-semibold mb-3 text-primary">Browser Requirements</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Modern web browser with WebGL 2.0 support</li>
                  <li>• JavaScript enabled</li>
                  <li>• Hardware acceleration enabled</li>
                  <li>• Minimum 4GB system RAM</li>
                  <li>• Stable internet connection</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3 text-primary">Graphics Requirements</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• DirectX 11 compatible graphics card</li>
                  <li>• Minimum 2GB VRAM for basic testing</li>
                  <li>• Updated graphics drivers</li>
                  <li>• Adequate cooling solution</li>
                  <li>• Sufficient power supply</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default TechnicalDetails;