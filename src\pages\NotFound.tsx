import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Home, Search, AlertTriangle } from "lucide-react";
import SEO from "@/components/SEO";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <SEO 
        title="404 - Page Not Found | GPU Stress Test"
        description="The page you're looking for doesn't exist. Return to GPU Stress Test to access our free online GPU testing tools and graphics card benchmarking services."
        keywords="404 error, page not found, GPU Stress Test, graphics card testing"
        noindex={true}
      />
      <Card variant="tech" className="max-w-lg w-full p-8 text-center">
        <div className="space-y-6">
          {/* Error Icon */}
          <div className="flex justify-center">
            <div className="p-4 bg-gradient-primary rounded-full shadow-glow-primary">
              <AlertTriangle className="h-12 w-12 text-primary-foreground" />
            </div>
          </div>

          {/* Error Code */}
          <div>
            <h1 className="text-6xl font-bold gradient-text mb-2">404</h1>
            <h2 className="text-2xl font-semibold text-foreground mb-2">Page Not Found</h2>
            <p className="text-muted-foreground">
              Sorry, the page you're looking for doesn't exist. It might have been moved, 
              deleted, or you entered the wrong URL.
            </p>
          </div>

          {/* Suggested Path */}
          <div className="text-sm text-muted-foreground">
            <p>You tried to access: <code className="bg-muted px-2 py-1 rounded text-foreground">{location.pathname}</code></p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button variant="hero" asChild>
              <Link to="/">
                <Home className="w-4 h-4 mr-2" />
                Go Home
              </Link>
            </Button>
            <Button variant="tech" asChild>
              <Link to="/#test">
                <Search className="w-4 h-4 mr-2" />
                Start GPU Test
              </Link>
            </Button>
          </div>

          {/* Additional Help */}
          <div className="pt-4 border-t border-border/50">
            <p className="text-sm text-muted-foreground mb-3">
              Looking for something specific?
            </p>
            <div className="flex flex-wrap gap-2 justify-center text-xs">
              <Link to="/" className="text-primary hover:text-primary-glow transition-colors">
                GPU Testing
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link to="/faq" className="text-primary hover:text-primary-glow transition-colors">
                FAQ
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link to="/contact" className="text-primary hover:text-primary-glow transition-colors">
                Support
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link to="/privacy" className="text-primary hover:text-primary-glow transition-colors">
                Privacy
              </Link>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default NotFound;
