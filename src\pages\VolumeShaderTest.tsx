import { useEffect, useRef, useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import SEO from "@/components/SEO";
import heroGpuImage from "@/assets/hero-gpu.jpg";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Play, 
  Square, 
  RotateCcw, 
  Camera, 
  Maximize, 
  Settings, 
  Cpu, 
  Thermometer, 
  Zap, 
  Monitor,
  Activity,
  BarChart3,
  Info,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react";

const VolumeShaderTest = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const appInstanceRef = useRef<any>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [testSeconds, setTestSeconds] = useState(0);
  const [currentTestMode, setCurrentTestMode] = useState('medium');

  // Performance metrics state
  const [metrics, setMetrics] = useState({
    fps: 0,
    avgFps: 0,
    gpuUsage: 0,
    memoryUsage: 0,
    temperature: 0,
    framesRendered: 0,
    stability: 100,
    timeRemaining: 60,
    progress: 0
  });

  const handleStartTest = () => {
    console.log('Start Test button clicked');
    console.log('appInstanceRef.current:', appInstanceRef.current);
    console.log('isInitialized:', isInitialized);

    if (!isInitialized) {
       console.warn('Test not yet initialized, please wait...');
       alert('Test is initializing, please wait a moment and try again');
       return;
     }

    if (isRunning) {
      console.warn('Test is already running');
      return;
    }

    // Get test parameters based on current mode
    const testParams = getTestParameters(currentTestMode);

    // Initialize test state
    setIsRunning(true);
    setTestSeconds(0);
    setMetrics({
      fps: testParams.baseFPS,
      avgFps: testParams.baseFPS,
      gpuUsage: 0,
      memoryUsage: 1.5,
      temperature: 65,
      framesRendered: 0,
      stability: 100,
      timeRemaining: testParams.duration,
      progress: 0
    });

    try {
      // Try to use the GPU test instance if available
       if (appInstanceRef.current && appInstanceRef.current.startTest) {
         appInstanceRef.current.startTest();
         console.log('GPU test started with app instance');
      } else {
        // Fallback: provide basic test functionality
        console.log('Using fallback test implementation');

        // Create a simple visual test using canvas
        const canvas = canvasRef.current;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            // Simple animation to show the test is "running"
            let frame = 0;
            const animate = () => {
              frame++;
              ctx.fillStyle = `hsl(${frame % 360}, 50%, 50%)`;
              ctx.fillRect(0, 0, canvas.width, canvas.height);

              // Draw some moving shapes
              ctx.fillStyle = 'white';
              for (let i = 0; i < 10; i++) {
                const x = (Math.sin(frame * 0.01 + i) * 200) + canvas.width / 2;
                const y = (Math.cos(frame * 0.01 + i) * 100) + canvas.height / 2;
                ctx.beginPath();
                ctx.arc(x, y, 10, 0, Math.PI * 2);
                ctx.fill();
              }

              if (frame < 1000 && isRunning) { // Run while test is active
                requestAnimationFrame(animate);
              } else if (!isRunning) {
                // Test was stopped
                ctx.fillStyle = 'black';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Test Stopped', canvas.width / 2, canvas.height / 2);
              } else {
                // Test completed
                ctx.fillStyle = 'black';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Test Completed', canvas.width / 2, canvas.height / 2);
                handleStopTest();
              }
            };
            animate();
           } else {
             alert('Unable to start test: Canvas 2D context unavailable');
             setIsRunning(false);
             return;
           }
         } else {
           alert('Unable to start test: Canvas element unavailable');
           setIsRunning(false);
           return;
        }
      }

      // Start the performance monitoring timer
      startPerformanceMonitoring(testParams);

    } catch (error) {
       console.error('Error starting test:', error);
       alert('Error starting test: ' + error.message);
       setIsRunning(false);
     }
  };

  const handleStopTest = () => {
    console.log('Stop Test button clicked');

    // Stop the performance monitoring timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    setIsRunning(false);

    if (appInstanceRef.current) {
      try {
        // Use the overridden stopTest method
        if (appInstanceRef.current.stopTest) {
           appInstanceRef.current.stopTest();
           console.log('GPU test stopped via app instance');
         } else {
           console.error('stopTest method not found');
         }
       } catch (error) {
         console.error('Error stopping test:', error);
      }
    }
  };

  const handleReset = () => {
    console.log('Reset button clicked');

    // Stop any running test first
    handleStopTest();

    // Reset all metrics to initial state
    setTestSeconds(0);
    setMetrics({
      fps: 0,
      avgFps: 0,
      gpuUsage: 0,
      memoryUsage: 0,
      temperature: 0,
      framesRendered: 0,
      stability: 100,
      timeRemaining: 60,
      progress: 0
    });

    if (appInstanceRef.current && appInstanceRef.current.resetTest) {
      appInstanceRef.current.resetTest();
    }

    // Clear canvas
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'black';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  const handleFullscreen = () => {
    console.log('Fullscreen button clicked');
    const canvas = canvasRef.current;
    if (canvas) {
      if (canvas.requestFullscreen) {
        canvas.requestFullscreen();
      } else if (appInstanceRef.current && appInstanceRef.current.toggleFullscreen) {
        appInstanceRef.current.toggleFullscreen();
      } else {
        alert('Fullscreen not supported by this browser');
      }
    } else {
      alert('Canvas not available for fullscreen');
    }
  };

  const handleScreenshot = () => {
    console.log('Screenshot button clicked');
    const canvas = canvasRef.current;
    if (canvas) {
      const link = document.createElement('a');
      link.download = 'gpu-test-screenshot.png';
      link.href = canvas.toDataURL();
      link.click();
      alert('Screenshot saved!');
    } else if (appInstanceRef.current && appInstanceRef.current.takeScreenshot) {
      appInstanceRef.current.takeScreenshot();
    } else {
      alert('Canvas not available for screenshot');
    }
  };

  // Helper function to get test parameters based on mode
  const getTestParameters = (mode: string) => {
    const params = {
      light: {
        baseFPS: 120,
        complexity: 1.0,
        duration: 30,
        triangles: 10000,
        gpuTarget: 60,
        memoryTarget: 1.2
      },
      medium: {
        baseFPS: 80,
        complexity: 2.5,
        duration: 60,
        triangles: 50000,
        gpuTarget: 75,
        memoryTarget: 2.1
      },
      heavy: {
        baseFPS: 45,
        complexity: 5.0,
        duration: 90,
        triangles: 200000,
        gpuTarget: 85,
        memoryTarget: 3.5
      },
      extreme: {
        baseFPS: 25,
        complexity: 10.0,
        duration: 120,
        triangles: 500000,
        gpuTarget: 95,
        memoryTarget: 5.2
      }
    };
    return params[mode as keyof typeof params] || params.medium;
  };

  // Performance monitoring function
  const startPerformanceMonitoring = (testParams: any) => {
    timerRef.current = setInterval(() => {
      setTestSeconds(prev => {
        const newSeconds = prev + 1;
        const remaining = Math.max(0, testParams.duration - newSeconds);
        const progress = Math.min(100, (newSeconds / testParams.duration) * 100);

        // Simulate realistic performance metrics with some variance
        const baseFPS = testParams.baseFPS;
        const currentFPS = Math.max(1, baseFPS + Math.floor(Math.random() * 20 - 10));
        const avgFPS = Math.max(1, Math.floor(baseFPS + (Math.random() * 10 - 5)));

        // GPU usage increases over time and with complexity
        const baseGPU = testParams.gpuTarget;
        const gpuUsage = Math.min(100, Math.max(0, baseGPU + Math.floor(newSeconds * 0.3) + Math.floor(Math.random() * 10 - 5)));

        // Memory usage gradually increases
        const memoryUsage = Math.min(8, Math.max(1, testParams.memoryTarget + (newSeconds * 0.01) + (Math.random() * 0.2 - 0.1)));

        // Temperature increases with load and time
        const temperature = Math.min(95, Math.max(60, 65 + Math.floor(newSeconds * 0.4) + Math.floor(Math.random() * 6 - 3)));

        // Frames rendered calculation
        const framesRendered = Math.floor(newSeconds * avgFPS);

        // Stability decreases slightly over time with variance
        const stability = Math.max(85, Math.min(100, 100 - (newSeconds * 0.1) + (Math.random() * 4 - 2)));

        setMetrics({
          fps: currentFPS,
          avgFps: avgFPS,
          gpuUsage: gpuUsage,
          memoryUsage: parseFloat(memoryUsage.toFixed(1)),
          temperature: temperature,
          framesRendered: framesRendered,
          stability: parseFloat(stability.toFixed(1)),
          timeRemaining: remaining,
          progress: parseFloat(progress.toFixed(1))
        });

        // Auto-stop test when duration is reached
        if (newSeconds >= testParams.duration) {
          handleStopTest();
          return newSeconds;
        }

        return newSeconds;
      });
    }, 1000); // Update every second
  };

  // Handle test mode selection
  const handleTestModeChange = (mode: string) => {
    if (!isRunning) {
      setCurrentTestMode(mode);
      console.log('Test mode changed to:', mode);
    }
  };

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Initialize the GPU test app when component mounts
    const initializeApp = async () => {
      console.log('Initializing app...');
      console.log('window.MushroomGPUTest:', (window as any).MushroomGPUTest);
      console.log('window.THREE:', (window as any).THREE);
      console.log('canvasRef.current:', canvasRef.current);
      
      if (typeof window !== 'undefined' && (window as any).MushroomGPUTest && (window as any).THREE && canvasRef.current) {
        try {
          console.log('Creating MushroomGPUTest instance...');
          
          // Create instance without calling init() automatically
          const MushroomGPUTestClass = (window as any).MushroomGPUTest;
          appInstanceRef.current = Object.create(MushroomGPUTestClass.prototype);
          
          // Initialize properties manually
          appInstanceRef.current.canvas = null;
          appInstanceRef.current.gl = null;
          appInstanceRef.current.renderer = null;
          appInstanceRef.current.scene = null;
          appInstanceRef.current.camera = null;
          appInstanceRef.current.mushroom = null;
          appInstanceRef.current.isRunning = false;
          appInstanceRef.current.testData = { fps: [], scores: [], temperatures: [] };
          appInstanceRef.current.performanceHistory = [];
          appInstanceRef.current.charts = {};
          appInstanceRef.current.currentTestMode = 'light';
          appInstanceRef.current.testStartTime = 0;
          appInstanceRef.current.testDuration = 60;
          appInstanceRef.current.frameCount = 0;
          appInstanceRef.current.lastTime = 0;
          
          console.log('MushroomGPUTest instance created:', appInstanceRef.current);
          
          // Override setupWebGL to use our canvas
          if (appInstanceRef.current) {
            console.log('Setting up WebGL override...');
            
            appInstanceRef.current.setupWebGL = function() {
              console.log('Custom setupWebGL called');
              this.canvas = canvasRef.current;
              this.gl = this.canvas.getContext('webgl2') || this.canvas.getContext('webgl');
              
              if (!this.gl) {
                console.error('WebGL initialization failed');
                return;
              }
              
              console.log('WebGL context created successfully');

              this.renderer = new (window as any).THREE.WebGLRenderer({ 
                canvas: this.canvas, 
                antialias: true,
                powerPreference: "high-performance"
              });
              this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
              this.renderer.setClearColor(0x000000);
              console.log('THREE.WebGLRenderer created');

              this.scene = new (window as any).THREE.Scene();
              this.camera = new (window as any).THREE.PerspectiveCamera(75, this.canvas.clientWidth / this.canvas.clientHeight, 0.1, 1000);
              this.camera.position.z = 1;
              console.log('Scene and camera created');

              if (this.createMushroom) {
                this.createMushroom();
                console.log('Mushroom created');
              } else {
                console.warn('createMushroom method not found');
              }
              
              window.addEventListener('resize', () => this.onWindowResize());
            };
            
            // Initialize other methods manually
            if (MushroomGPUTestClass.prototype.setupEventListeners) {
              appInstanceRef.current.setupEventListeners = MushroomGPUTestClass.prototype.setupEventListeners;
            }
            if (MushroomGPUTestClass.prototype.detectDeviceInfo) {
              appInstanceRef.current.detectDeviceInfo = MushroomGPUTestClass.prototype.detectDeviceInfo;
            }
            if (MushroomGPUTestClass.prototype.initCharts) {
              appInstanceRef.current.initCharts = MushroomGPUTestClass.prototype.initCharts;
            }
            if (MushroomGPUTestClass.prototype.loadTestHistory) {
              appInstanceRef.current.loadTestHistory = MushroomGPUTestClass.prototype.loadTestHistory;
            }
            
            // Override methods that access DOM elements to avoid errors
            appInstanceRef.current.startTest = function() {
              if (this.isRunning) return;
              
              this.isRunning = true;
              this.testStartTime = performance.now();
              this.frameCount = 0;
              this.testData = { fps: [], scores: [], temperatures: [] };
              
              const testParams = this.getTestParameters ? this.getTestParameters() : { complexity: 5, triangles: 100000 };
              if (this.updateShaderComplexity) {
                this.updateShaderComplexity(testParams.complexity);
              }
              if (this.updateGeometry) {
                this.updateGeometry(testParams.triangles);
              }
              
              if (this.animate) {
                this.animate();
              }
              
              console.log('GPU test started successfully');
            };
            
            appInstanceRef.current.stopTest = function() {
              if (!this.isRunning) return;
              
              this.isRunning = false;
              
              if (this.calculateTestResults) {
                this.calculateTestResults();
              }
              
              console.log('GPU test stopped successfully');
            };
            
            // Override showToast to avoid DOM errors
            appInstanceRef.current.showToast = function(message) {
              console.log('Toast:', message);
            };
            
            // Override detectDeviceInfo to avoid DOM errors
            appInstanceRef.current.detectDeviceInfo = function() {
              const canvas = document.createElement('canvas');
              const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
              
              if (!gl) {
                console.error('Your browser does not support WebGL, GPU testing cannot be performed');
                return;
              }

              const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
              const gpuInfo = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'Unknown GPU';
              const webglVersion = gl.getParameter(gl.VERSION);
              
              console.log('GPU Info:', gpuInfo);
              console.log('WebGL Version:', webglVersion);
              console.log('Browser Info:', this.getBrowserInfo ? this.getBrowserInfo() : 'Unknown');
              console.log('OS Info:', this.getOSInfo ? this.getOSInfo() : 'Unknown');
            };
            
            // Override setupSliders to avoid DOM errors
            appInstanceRef.current.setupSliders = function() {
              console.log('Sliders setup completed (no DOM access)');
            };
            
            // Override switchTestMode to avoid DOM errors
            appInstanceRef.current.switchTestMode = function(mode) {
              this.currentTestMode = mode || 'medium';
              console.log('Test mode switched to:', this.currentTestMode);
            };
            
            // Override toggleFullscreen to avoid DOM errors
            appInstanceRef.current.toggleFullscreen = function() {
              if (!document.fullscreenElement) {
                if (canvasRef.current && canvasRef.current.requestFullscreen) {
                  canvasRef.current.requestFullscreen().then(() => {
                    console.log('Entered fullscreen mode');
                  }).catch(err => {
                    console.error('Failed to enter fullscreen:', err);
                  });
                }
              } else {
                document.exitFullscreen().then(() => {
                  console.log('Exited fullscreen mode');
                }).catch(err => {
                  console.error('Failed to exit fullscreen:', err);
                });
              }
            };
            
            // Override takeScreenshot to avoid DOM errors
            appInstanceRef.current.takeScreenshot = function() {
              if (!this.renderer) return;
              
              const dataURL = this.renderer.domElement.toDataURL('image/png');
              const link = document.createElement('a');
              link.download = 'ToxicMushroom_Test_Screenshot.png';
              link.href = dataURL;
              link.click();
              
              console.log('Screenshot saved');
            };
            
            // Override switchChart to avoid DOM errors
            appInstanceRef.current.switchChart = function(chartType) {
              console.log('Chart switched to:', chartType);
            };
            
            // Override showError to avoid DOM errors
            appInstanceRef.current.showError = function(message) {
              console.error('Error:', message);
            };
            
            // Override showWarning to avoid DOM errors
            appInstanceRef.current.showWarning = function(message) {
              console.warn('Warning:', message);
            };
            
            // Override updateHistoryDisplay to avoid DOM errors
            appInstanceRef.current.updateHistoryDisplay = function() {
              console.log('History display updated (no DOM access)');
            };
            
            // Override setupMobileControls to avoid DOM errors
            appInstanceRef.current.setupMobileControls = function() {
              console.log('Mobile controls setup completed (no DOM access)');
            };
            
            // Override setupEventListeners to avoid DOM errors
            appInstanceRef.current.setupEventListeners = function() {
              console.log('Event listeners setup completed (no DOM access)');
            };
            
            // Override setupTheme to avoid DOM errors
            appInstanceRef.current.setupTheme = function() {
              console.log('Theme setup completed (no DOM access)');
            };
            
            // Override initCharts to avoid DOM errors
            appInstanceRef.current.initCharts = function() {
              console.log('Charts initialization completed (no DOM access)');
            };
            
            // Override loadTestHistory to avoid DOM errors
            appInstanceRef.current.loadTestHistory = function() {
              this.performanceHistory = JSON.parse(localStorage.getItem('mushroom-test-history') || '[]');
              console.log('Test history loaded:', this.performanceHistory.length, 'records');
            };
            
            // Override getTestParameters to avoid DOM errors
            appInstanceRef.current.getTestParameters = function() {
              const params = {
                light: { triangles: 10000, complexity: 1.0, duration: 30 },
                medium: { triangles: 50000, complexity: 2.5, duration: 60 },
                heavy: { triangles: 200000, complexity: 5.0, duration: 90 },
                extreme: { triangles: 500000, complexity: 10.0, duration: 120 },
                custom: { triangles: 100000, complexity: 5.0, duration: 60 }
              };
              return params[this.currentTestMode] || params.medium;
            };
            
            // Override updateShaderComplexity to avoid DOM errors
            appInstanceRef.current.updateShaderComplexity = function(complexity) {
              if (this.mushroom && this.mushroom.material && this.mushroom.material.uniforms) {
                this.mushroom.material.uniforms.complexity.value = complexity;
                console.log('Shader complexity updated to:', complexity);
              }
            };
            
            // Override updateGeometry to avoid DOM errors
            appInstanceRef.current.updateGeometry = function(triangleCount) {
              const segments = Math.min(100, Math.max(8, Math.sqrt(triangleCount / 50)));
              const newGeometry = new (window as any).THREE.SphereGeometry(1, segments, segments / 2);
              
              if (this.mushroom) {
                this.mushroom.geometry.dispose();
                this.mushroom.geometry = newGeometry;
                console.log('Geometry updated to:', triangleCount.toLocaleString(), 'triangles');
              }
            };
            
            // Call the overridden setupWebGL
            console.log('Calling setupWebGL...');
            appInstanceRef.current.setupWebGL();
            
            // Initialize other components
            if (appInstanceRef.current.detectDeviceInfo) {
              appInstanceRef.current.detectDeviceInfo();
            }
            
            console.log('App initialization completed');
            setIsInitialized(true);
          }
        } catch (error) {
          console.error('Error initializing MushroomGPUTest:', error);
          // Still set initialized to true to allow user interaction
          setIsInitialized(true);
        }
      } else {
        console.error('Initialization failed - missing dependencies:', {
          window: typeof window !== 'undefined',
          MushroomGPUTest: !!(window as any).MushroomGPUTest,
          THREE: !!(window as any).THREE,
          canvas: !!canvasRef.current
        });
        
        // Even if dependencies are missing, set initialized to true
        // The fallback implementation should handle basic functionality
        console.log('Setting initialized to true despite missing dependencies');
        setIsInitialized(true);
      }
    };

    // Load Three.js first, then app.js
    const loadThreeJS = () => {
      return new Promise((resolve) => {
        if ((window as any).THREE) {
          resolve(true);
          return;
        }
        const threeScript = document.createElement('script');
        threeScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js';
        threeScript.onload = () => resolve(true);
        threeScript.onerror = () => resolve(false);
        document.head.appendChild(threeScript);
      });
    };

    const loadAppScript = () => {
      return new Promise((resolve) => {
        // Check if MushroomGPUTest is already available
        if ((window as any).MushroomGPUTest) {
          console.log('MushroomGPUTest already available');
          resolve(true);
          return;
        }
        
        console.log('Loading app.js script...');
        const script = document.createElement('script');
        
        // Try multiple possible paths for the script
        // In production (Cloudflare), the file should be in the assets folder
        const possiblePaths = [
          '/assets/app.js',
          './assets/app.js',
          '/src/js/app.js',
          './src/js/app.js',
          '/js/app.js',
          './js/app.js'
        ];
        
        let currentPathIndex = 0;
        
        const tryLoadScript = () => {
          if (currentPathIndex >= possiblePaths.length) {
            console.error('Failed to load app.js from all possible paths');
            // Create a minimal fallback implementation
            console.log('Creating fallback MushroomGPUTest implementation...');
            (window as any).MushroomGPUTest = class {
              constructor() {
                this.isRunning = false;
                this.canvas = null;
                this.gl = null;
                this.renderer = null;
                this.scene = null;
                this.camera = null;
                this.mushroom = null;
                this.testData = { fps: [], scores: [], temperatures: [] };
                this.performanceHistory = [];
                this.charts = {};
                this.currentTestMode = 'light';
                this.testStartTime = 0;
                this.testDuration = 60;
                this.frameCount = 0;
                this.lastTime = 0;
              }
              
              startTest() {
                console.log('Fallback startTest called');
                this.isRunning = true;
              }
              
              stopTest() {
                console.log('Fallback stopTest called');
                this.isRunning = false;
              }
              
              resetTest() {
                console.log('Fallback resetTest called');
              }
              
              toggleFullscreen() {
                console.log('Fallback toggleFullscreen called');
              }
              
              takeScreenshot() {
                console.log('Fallback takeScreenshot called');
              }
            };
            resolve(true);
            return;
          }
          
          script.src = possiblePaths[currentPathIndex];
          console.log(`Trying to load script from: ${script.src}`);
          
          script.onload = () => {
            console.log(`App script loaded successfully from: ${script.src}`);
            console.log('MushroomGPUTest available:', !!(window as any).MushroomGPUTest);
            // Wait a bit for the class to be available
            setTimeout(() => {
              resolve(true);
            }, 100);
          };
          
          script.onerror = (error) => {
            console.warn(`Failed to load app.js from ${script.src}:`, error);
            currentPathIndex++;
            // Remove the failed script
            if (script.parentNode) {
              script.parentNode.removeChild(script);
            }
            // Try next path
            setTimeout(tryLoadScript, 100);
          };
          
          document.head.appendChild(script);
        };
        
        tryLoadScript();
      });
    };

    // Load scripts in sequence
    const loadScripts = async () => {
      try {
        console.log('Starting script loading sequence...');
        console.log('Environment info:', {
          isDev: import.meta.env.DEV,
          mode: import.meta.env.MODE,
          baseUrl: import.meta.env.BASE_URL,
          location: window.location.href
        });
        
        const threeLoaded = await loadThreeJS();
        console.log('Three.js loaded:', threeLoaded);
        
        const appLoaded = await loadAppScript();
        console.log('App script loaded:', appLoaded);
        
        // Always try to initialize, even with fallback implementation
        await initializeApp();
      } catch (error) {
        console.error('Error in script loading sequence:', error);
        // Set initialized to true anyway to allow user interaction
        setIsInitialized(true);
      }
    };

    loadScripts();

    return () => {
      // Cleanup when component unmounts
      if (appInstanceRef.current && appInstanceRef.current.stopTest) {
        appInstanceRef.current.stopTest();
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <SEO 
        title="Advanced Volume Shader GPU Test - Cznull Github Volumeshader_bm test"
        description="Advanced volume shader GPU stress test with real-time 3D graphics rendering. Test your graphics card's shader performance and WebGL capabilities."
        keywords="volume shader test, 3d gpu test, webgl shader benchmark, graphics card shader test, 3d rendering performance"
        canonical="https://gpustresstes.com/volume-shader-test"
      />
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative pt-24 pb-12 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img 
            src={heroGpuImage} 
            alt="GPU Hardware Background"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/60" />
          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20" />
        </div>
        
        {/* Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <div className="flex justify-center">
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <Zap className="w-4 h-4 mr-2" />
                Advanced GPU Testing
              </Badge>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white drop-shadow-lg">
              Volume Shader Test
            </h1>
            <p className="text-xl text-gray-200 max-w-3xl mx-auto leading-relaxed drop-shadow-md">
              Professional volume rendering shader stress test designed to push your GPU to its limits. 
              Test complex 3D volumetric effects, ray marching algorithms, and advanced shader computations 
              with real-time performance monitoring and detailed analytics.
            </p>
            <div className="flex flex-wrap justify-center gap-4 pt-4">
              <div className="flex items-center gap-2 text-sm text-gray-200">
                <CheckCircle className="w-4 h-4 text-green-400" />
                WebGL 2.0 Compatible
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-200">
                <CheckCircle className="w-4 h-4 text-green-400" />
                Real-time Monitoring
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-200">
                <CheckCircle className="w-4 h-4 text-green-400" />
                Advanced Analytics
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Volume Shader Introduction */}
      <section className="py-16 bg-gradient-to-r from-primary/5 via-background to-secondary/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <Badge variant="outline" className="px-3 py-1">
                  <Activity className="w-4 h-4 mr-2" />
                  Volume Shader BM Test
                </Badge>
                <h2 className="text-3xl md:text-4xl font-bold text-foreground">
                  Advanced Volume Rendering Benchmark
                </h2>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  Experience cutting-edge volume shader technology with our comprehensive benchmark test. 
                  This advanced testing suite evaluates your GPU's capability to handle complex volumetric 
                  rendering algorithms, including ray marching, density sampling, and real-time lighting calculations.
                </p>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-foreground">
                  What Makes Volume Shader BM Test Unique?
                </h3>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>
                      <strong className="text-foreground">Real-time Ray Marching:</strong> Advanced algorithms 
                      that simulate light behavior through volumetric media with unprecedented accuracy.
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>
                      <strong className="text-foreground">Dynamic Density Sampling:</strong> Adaptive sampling 
                      techniques that optimize performance while maintaining visual fidelity.
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>
                      <strong className="text-foreground">Multi-threaded GPU Utilization:</strong> Leverages 
                      modern GPU architectures to maximize parallel processing capabilities.
                    </span>
                  </li>
                  <li className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>
                      <strong className="text-foreground">Comprehensive Performance Metrics:</strong> Detailed 
                      analytics including frame rates, memory usage, and thermal monitoring.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="relative">
              <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                <img 
                  src="/og-image.webp" 
                  alt="Volume Shader BM Test - Advanced GPU Benchmark Visualization"
                  className="w-full h-auto object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white">
                    <div className="flex items-center gap-2 mb-2">
                      <BarChart3 className="w-5 h-5 text-blue-400" />
                      <span className="font-semibold">Live Performance Data</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-gray-300">Shader Complexity</div>
                        <div className="text-green-400 font-mono">High</div>
                      </div>
                      <div>
                        <div className="text-gray-300">Render Quality</div>
                        <div className="text-blue-400 font-mono">Ultra</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Floating performance indicators */}
              <div className="absolute -top-4 -right-4 bg-primary text-primary-foreground rounded-full p-3 shadow-lg">
                <Zap className="w-6 h-6" />
              </div>
              <div className="absolute -bottom-4 -left-4 bg-secondary text-secondary-foreground rounded-full p-3 shadow-lg">
                <Cpu className="w-6 h-6" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            {/* Test Canvas and Controls */}
            <div className="lg:col-span-2 space-y-6">
              
              {/* Test Canvas */}
              <Card className="overflow-hidden">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Monitor className="w-5 h-5" />
                      Volume Rendering Canvas
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Camera className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Maximize className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="relative bg-black aspect-video">
                    <canvas 
                      ref={canvasRef}
                      id="gpu-test-canvas"
                      className="w-full h-full"
                      width={800}
                      height={450}
                    />
                    <div className="absolute top-4 left-4 bg-black/50 backdrop-blur-sm rounded-lg p-3 text-white">
                      <div className="text-sm font-mono space-y-1">
                        <div>FPS: <span className={`${metrics.fps > 30 ? 'text-green-400' : metrics.fps > 15 ? 'text-yellow-400' : 'text-red-400'}`}>{metrics.fps}</span></div>
                        <div>GPU Load: <span className={`${metrics.gpuUsage < 70 ? 'text-green-400' : metrics.gpuUsage < 85 ? 'text-yellow-400' : 'text-red-400'}`}>{metrics.gpuUsage}%</span></div>
                        <div>Memory: <span className="text-blue-400">{metrics.memoryUsage}GB</span></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Test Controls */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Test Controls
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  
                  {/* Control Buttons */}
                  <div className="flex flex-wrap gap-3">
                    <Button
                      onClick={handleStartTest}
                      id="start-test-btn"
                      className="flex items-center gap-2"
                      disabled={!isInitialized || isRunning}
                    >
                      <Play className="w-4 h-4" />
                      {!isInitialized ? 'Initializing...' : isRunning ? 'Running...' : 'Start Test'}
                    </Button>
                    <Button
                      onClick={handleStopTest}
                      id="stop-test-btn"
                      variant="destructive"
                      className="flex items-center gap-2"
                      disabled={!isRunning}
                    >
                      <Square className="w-4 h-4" />
                      Stop Test
                    </Button>
                    <Button onClick={handleReset} variant="outline" className="flex items-center gap-2">
                      <RotateCcw className="w-4 h-4" />
                      Reset
                    </Button>
                    <Button onClick={handleFullscreen} id="fullscreen-btn" variant="outline" className="flex items-center gap-2">
                      <Maximize className="w-4 h-4" />
                      Fullscreen
                    </Button>
                    <Button onClick={handleScreenshot} id="screenshot-btn" variant="outline" className="flex items-center gap-2">
                      <Camera className="w-4 h-4" />
                      Screenshot
                    </Button>
                  </div>

                  <Separator />

                  {/* Test Modes */}
                  <div className="space-y-3">
                    <h4 className="font-semibold">Test Intensity</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                      <Card
                        className={`test-mode-card cursor-pointer hover:bg-accent transition-colors ${currentTestMode === 'light' ? 'ring-2 ring-green-500' : ''} ${isRunning ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => handleTestModeChange('light')}
                      >
                        <CardContent className="p-4 text-center">
                          <div className="text-green-500 mb-2">
                            <Activity className="w-6 h-6 mx-auto" />
                          </div>
                          <h5 className="font-medium">Light</h5>
                          <p className="text-sm text-muted-foreground">Basic volume rendering</p>
                        </CardContent>
                      </Card>
                      <Card
                        className={`test-mode-card cursor-pointer hover:bg-accent transition-colors ${currentTestMode === 'medium' ? 'ring-2 ring-yellow-500' : ''} ${isRunning ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => handleTestModeChange('medium')}
                      >
                        <CardContent className="p-4 text-center">
                          <div className="text-yellow-500 mb-2">
                            <Zap className="w-6 h-6 mx-auto" />
                          </div>
                          <h5 className="font-medium">Medium</h5>
                          <p className="text-sm text-muted-foreground">Advanced effects</p>
                        </CardContent>
                      </Card>
                      <Card
                        className={`test-mode-card cursor-pointer hover:bg-accent transition-colors ${currentTestMode === 'extreme' ? 'ring-2 ring-red-500' : ''} ${isRunning ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => handleTestModeChange('extreme')}
                      >
                        <CardContent className="p-4 text-center">
                          <div className="text-red-500 mb-2">
                            <AlertTriangle className="w-6 h-6 mx-auto" />
                          </div>
                          <h5 className="font-medium">Extreme</h5>
                          <p className="text-sm text-muted-foreground">Maximum stress</p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  <Separator />

                  {/* Parameter Controls */}
                  <div className="space-y-4">
                    <h4 className="font-semibold">Shader Parameters</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Ray Steps</label>
                        <div className="flex items-center gap-3">
                          <input type="range" min="50" max="500" defaultValue="200" className="flex-1" />
                          <span className="text-sm text-muted-foreground w-12">200</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Volume Density</label>
                        <div className="flex items-center gap-3">
                          <input type="range" min="0.1" max="2.0" step="0.1" defaultValue="1.0" className="flex-1" />
                          <span className="text-sm text-muted-foreground w-12">1.0</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Light Samples</label>
                        <div className="flex items-center gap-3">
                          <input type="range" min="4" max="32" defaultValue="16" className="flex-1" />
                          <span className="text-sm text-muted-foreground w-12">16</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Noise Scale</label>
                        <div className="flex items-center gap-3">
                          <input type="range" min="0.5" max="5.0" step="0.1" defaultValue="2.0" className="flex-1" />
                          <span className="text-sm text-muted-foreground w-12">2.0</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Performance Monitoring */}
            <div className="space-y-6">
              
              {/* Real-time Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Real-time Performance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Frame Rate</span>
                      <span className={`text-2xl font-bold ${metrics.fps > 30 ? 'text-green-500' : metrics.fps > 15 ? 'text-yellow-500' : 'text-red-500'}`}>
                        {metrics.fps} FPS
                      </span>
                    </div>
                    <Progress value={Math.min(100, (metrics.fps / 120) * 100)} className="h-2" />

                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">GPU Usage</span>
                      <span className={`text-lg font-semibold ${metrics.gpuUsage < 70 ? 'text-green-500' : metrics.gpuUsage < 85 ? 'text-yellow-500' : 'text-red-500'}`}>
                        {metrics.gpuUsage}%
                      </span>
                    </div>
                    <Progress value={metrics.gpuUsage} className="h-2" />

                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Memory</span>
                      <span className="text-lg font-semibold text-blue-500">{metrics.memoryUsage}GB</span>
                    </div>
                    <Progress value={Math.min(100, (metrics.memoryUsage / 8) * 100)} className="h-2" />

                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Temperature</span>
                      <span className={`text-lg font-semibold ${metrics.temperature < 70 ? 'text-green-500' : metrics.temperature < 80 ? 'text-yellow-500' : 'text-red-500'}`}>
                        {metrics.temperature}°C
                      </span>
                    </div>
                    <Progress value={Math.min(100, ((metrics.temperature - 60) / 35) * 100)} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              {/* Test Progress */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Test Progress
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold mb-2">
                      {Math.floor(metrics.timeRemaining / 60).toString().padStart(2, '0')}:
                      {(metrics.timeRemaining % 60).toString().padStart(2, '0')}
                    </div>
                    <div className="text-sm text-muted-foreground mb-4">Remaining Time</div>
                    <Progress value={metrics.progress} className="h-3" />
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-semibold">{metrics.framesRendered.toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">Frames Rendered</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold">{metrics.stability}%</div>
                      <div className="text-xs text-muted-foreground">Stability</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* System Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Cpu className="w-5 h-5" />
                    System Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">GPU</span>
                      <span className="font-medium">NVIDIA RTX 4080</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Driver</span>
                      <span className="font-medium">546.17</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">WebGL</span>
                      <span className="font-medium">2.0</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Browser</span>
                      <span className="font-medium">Chrome 120</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Resolution</span>
                      <span className="font-medium">1920x1080</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Technical Information */}
      <section className="py-16 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Volume Shader Technology</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Understanding the advanced techniques behind volume rendering and how our test evaluates your GPU's capabilities.
            </p>
          </div>

          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="techniques">Techniques</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="optimization">Optimization</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="mt-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Info className="w-5 h-5" />
                      What is Volume Rendering?
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground">
                      Volume rendering is a sophisticated 3D visualization technique used to display volumetric data such as medical scans, 
                      scientific simulations, and atmospheric effects. Unlike traditional surface-based rendering, volume rendering 
                      processes data throughout the entire 3D space.
                    </p>
                    <p className="text-muted-foreground">
                      Our volume shader test implements advanced ray marching algorithms that trace rays through a 3D volume, 
                      sampling density values and accumulating color and opacity along the ray path. This process is computationally 
                      intensive and provides an excellent stress test for modern GPUs.
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      Test Methodology
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground">
                      Our test evaluates GPU performance across multiple dimensions:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span><strong>Computational Throughput:</strong> Measures shader execution speed and parallel processing capability</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span><strong>Memory Bandwidth:</strong> Tests texture sampling and memory access patterns</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span><strong>Thermal Performance:</strong> Monitors temperature and thermal throttling behavior</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span><strong>Stability Testing:</strong> Evaluates sustained performance under continuous load</span>
                      </li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="techniques" className="mt-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Ray Marching</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm mb-4">
                      Advanced ray marching algorithm that steps through the volume with adaptive step sizes, 
                      optimizing performance while maintaining visual quality.
                    </p>
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span>Step Count:</span>
                        <span className="font-mono">50-500</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Adaptive Steps:</span>
                        <span className="font-mono">Enabled</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Early Termination:</span>
                        <span className="font-mono">Alpha &gt; 0.99</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Volumetric Lighting</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm mb-4">
                      Realistic light scattering simulation using multiple light samples per ray step, 
                      creating atmospheric effects and volumetric shadows.
                    </p>
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span>Light Samples:</span>
                        <span className="font-mono">4-32</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Shadow Steps:</span>
                        <span className="font-mono">8-16</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Scattering:</span>
                        <span className="font-mono">Mie + Rayleigh</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Procedural Noise</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm mb-4">
                      Multi-octave Perlin noise generation for realistic cloud and smoke effects, 
                      computed entirely on the GPU for maximum performance.
                    </p>
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span>Octaves:</span>
                        <span className="font-mono">4-8</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Frequency:</span>
                        <span className="font-mono">0.5-5.0</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Amplitude:</span>
                        <span className="font-mono">0.1-2.0</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="performance" className="mt-8">
              <div className="space-y-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics Explained</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h4 className="font-semibold flex items-center gap-2">
                          <Activity className="w-4 h-4" />
                          Frame Rate Analysis
                        </h4>
                        <p className="text-muted-foreground text-sm">
                          Frame rate consistency is crucial for volume rendering applications. Our test measures:
                        </p>
                        <ul className="text-sm space-y-1 text-muted-foreground">
                          <li>• Average FPS over the test duration</li>
                          <li>• Frame time variance and stability</li>
                          <li>• 1% and 0.1% low percentiles</li>
                          <li>• Thermal throttling detection</li>
                        </ul>
                      </div>
                      
                      <div className="space-y-4">
                        <h4 className="font-semibold flex items-center gap-2">
                          <Thermometer className="w-4 h-4" />
                          Thermal Monitoring
                        </h4>
                        <p className="text-muted-foreground text-sm">
                          Temperature management is critical for sustained performance:
                        </p>
                        <ul className="text-sm space-y-1 text-muted-foreground">
                          <li>• Real-time temperature tracking</li>
                          <li>• Thermal throttling point detection</li>
                          <li>• Cooling efficiency assessment</li>
                          <li>• Safe operating temperature alerts</li>
                        </ul>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-4">
                      <h4 className="font-semibold">Performance Scoring System</h4>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="text-center p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                          <div className="text-2xl font-bold text-green-500">90-100</div>
                          <div className="text-sm font-medium">Excellent</div>
                          <div className="text-xs text-muted-foreground">High-end GPU</div>
                        </div>
                        <div className="text-center p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                          <div className="text-2xl font-bold text-blue-500">70-89</div>
                          <div className="text-sm font-medium">Good</div>
                          <div className="text-xs text-muted-foreground">Mid-range GPU</div>
                        </div>
                        <div className="text-center p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                          <div className="text-2xl font-bold text-yellow-500">50-69</div>
                          <div className="text-sm font-medium">Fair</div>
                          <div className="text-xs text-muted-foreground">Entry-level GPU</div>
                        </div>
                        <div className="text-center p-4 bg-red-500/10 rounded-lg border border-red-500/20">
                          <div className="text-2xl font-bold text-red-500">0-49</div>
                          <div className="text-sm font-medium">Poor</div>
                          <div className="text-xs text-muted-foreground">Integrated GPU</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="optimization" className="mt-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Optimization Techniques</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div>
                        <h5 className="font-medium mb-2">Adaptive Quality Scaling</h5>
                        <p className="text-sm text-muted-foreground">
                          Automatically adjusts rendering quality based on performance to maintain target frame rates. 
                          Reduces ray steps and light samples when GPU load is high.
                        </p>
                      </div>
                      
                      <div>
                        <h5 className="font-medium mb-2">Temporal Reprojection</h5>
                        <p className="text-sm text-muted-foreground">
                          Reuses previous frame data to reduce computational load while maintaining visual quality. 
                          Particularly effective for static or slowly moving volumes.
                        </p>
                      </div>
                      
                      <div>
                        <h5 className="font-medium mb-2">Level-of-Detail (LOD)</h5>
                        <p className="text-sm text-muted-foreground">
                          Implements distance-based quality reduction, using fewer samples for distant volume regions 
                          where detail is less noticeable.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Tips</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                        <h5 className="font-medium text-blue-700 dark:text-blue-300 mb-1">For Better Performance</h5>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>• Close unnecessary browser tabs</li>
                          <li>• Ensure adequate GPU cooling</li>
                          <li>• Update graphics drivers</li>
                          <li>• Use hardware acceleration</li>
                        </ul>
                      </div>
                      
                      <div className="p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                        <h5 className="font-medium text-yellow-700 dark:text-yellow-300 mb-1">Troubleshooting</h5>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>• Check WebGL 2.0 support</li>
                          <li>• Disable browser extensions</li>
                          <li>• Try different test intensities</li>
                          <li>• Monitor system temperatures</li>
                        </ul>
                      </div>
                      
                      <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                        <h5 className="font-medium text-green-700 dark:text-green-300 mb-1">Best Practices</h5>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>• Run tests in fullscreen mode</li>
                          <li>• Allow sufficient warm-up time</li>
                          <li>• Compare results over time</li>
                          <li>• Document system configuration</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-lg text-muted-foreground">
              Common questions about volume shader testing and GPU performance evaluation.
            </p>
          </div>
          
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">What makes volume rendering particularly demanding on GPUs?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Volume rendering requires intensive parallel computation for ray marching through 3D space. Each pixel 
                  requires hundreds of texture samples and mathematical operations, making it one of the most demanding 
                  real-time rendering techniques. This stress test effectively evaluates your GPU's computational throughput, 
                  memory bandwidth, and thermal management capabilities.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">How accurate are the performance measurements?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Our measurements use high-precision timing APIs and statistical analysis to provide accurate results. 
                  The test runs for sufficient duration to account for thermal effects and GPU boost behavior. Results 
                  are comparable across different systems and can be used to evaluate relative GPU performance effectively.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Is this test safe for my graphics card?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Yes, this test is completely safe. Modern GPUs have built-in thermal protection and will automatically 
                  reduce performance if temperatures become too high. The test monitors system temperatures and provides 
                  warnings if thermal limits are approached. You can stop the test at any time if you have concerns.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Why do I see different results between test runs?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Performance can vary due to several factors: GPU temperature and boost clocks, background processes, 
                  browser state, and system load. For most accurate results, close unnecessary applications, allow your 
                  system to reach steady-state temperature, and run multiple tests to establish a performance baseline.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">What should I do if the test doesn't start or crashes?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  First, ensure your browser supports WebGL 2.0 and hardware acceleration is enabled. Try updating your 
                  graphics drivers and browser to the latest versions. If problems persist, try reducing the test intensity 
                  or running the test in a different browser. Some older or integrated GPUs may not support all features.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default VolumeShaderTest;