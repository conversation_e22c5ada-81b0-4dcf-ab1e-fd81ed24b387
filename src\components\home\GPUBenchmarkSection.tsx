import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { BarChart3, Zap, Cpu, HardDrive, Trophy, TrendingUp } from "lucide-react";
import { useNavigate } from "react-router-dom";

const GPUBenchmarkSection = () => {
  const navigate = useNavigate();

  const handleBenchmarkClick = () => {
    navigate('/');
    // 延迟滚动到测试区域，确保页面已加载
    setTimeout(() => {
      const testSection = document.querySelector('[data-testid="test-section"]');
      if (testSection) {
        testSection.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };
  const benchmarkResults = [
    { name: "RTX 4090", score: 100, performance: "Exceptional", color: "bg-gradient-primary" },
    { name: "RTX 4080", score: 85, performance: "Excellent", color: "bg-gradient-secondary" },
    { name: "RTX 4070", score: 72, performance: "Very Good", color: "bg-accent" },
    { name: "RTX 3080", score: 68, performance: "Good", color: "bg-primary" },
    { name: "GTX 1660 Ti", score: 45, performance: "Fair", color: "bg-muted-foreground" },
  ];

  const testTypes = [
    {
      icon: BarChart3,
      title: "3D Rendering Benchmark",
      description: "Comprehensive 3D graphics performance testing with complex geometry and advanced shading techniques.",
      features: ["Real-time ray tracing", "Complex shader calculations", "High polygon count scenes", "Advanced lighting effects"]
    },
    {
      icon: Zap,
      title: "Compute Performance Test",
      description: "GPGPU computational workload testing for parallel processing and scientific computing applications.",
      features: ["OpenCL benchmarks", "CUDA performance tests", "Memory bandwidth testing", "Floating-point operations"]
    },
    {
      icon: Cpu,
      title: "Gaming Performance Analysis",
      description: "Game-like workload simulation with various graphics settings and resolution scaling tests.",
      features: ["4K resolution testing", "High refresh rate scenarios", "VR performance evaluation", "Multi-monitor support"]
    },
    {
      icon: HardDrive,
      title: "Memory Stress Testing",
      description: "VRAM utilization and memory bandwidth testing to identify potential memory-related issues.",
      features: ["VRAM capacity testing", "Memory leak detection", "Bandwidth optimization", "Error correction validation"]
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-background to-card/20" id="benchmark">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-gradient-card border border-primary/20 rounded-full text-sm font-medium shadow-glow-primary mb-6">
            <Trophy className="w-4 h-4 mr-2 text-primary" />
            Free Online GPU Benchmark Database
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="gradient-text">GPU Benchmark Comparison</span> & Performance Analysis
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Compare your graphics card performance against thousands of other GPUs in our comprehensive benchmark database. 
            Discover how your GPU performs in gaming, professional applications, and compute workloads with our free online testing suite.
          </p>
        </div>

        {/* Benchmark Results */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center mb-8">Popular GPU Performance Rankings</h3>
          <Card variant="tech" className="p-6">
            <div className="space-y-6">
              {benchmarkResults.map((gpu, index) => (
                <div key={gpu.name} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-gradient-primary rounded-full text-primary-foreground font-bold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground">{gpu.name}</h4>
                        <p className="text-sm text-muted-foreground">{gpu.performance} Performance</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-primary">{gpu.score}%</div>
                      <div className="text-sm text-muted-foreground">Relative Score</div>
                    </div>
                  </div>
                  <Progress value={gpu.score} className="h-3" />
                </div>
              ))}
            </div>
            <div className="mt-6 p-4 bg-muted/30 rounded-lg">
              <p className="text-sm text-muted-foreground text-center">
                * Benchmark scores based on comprehensive testing including 3D rendering, compute performance, and gaming workloads. 
                Test your GPU with our free online stress test to see how it compares.
              </p>
            </div>
          </Card>
        </div>

        {/* Benchmark Test Types */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-center mb-12">
            Comprehensive GPU Testing Suite
          </h3>
          <div className="grid md:grid-cols-2 gap-8">
            {testTypes.map((test, index) => (
              <Card key={index} variant="glow" className="group hover:shadow-glow-primary transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-gradient-secondary rounded-lg shadow-glow-secondary group-hover:shadow-glow-accent transition-all duration-300">
                      <test.icon className="h-6 w-6 text-secondary-foreground" />
                    </div>
                    <CardTitle className="text-xl">{test.title}</CardTitle>
                  </div>
                  <CardDescription className="text-base leading-relaxed">
                    {test.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <h5 className="font-medium text-foreground mb-3">Key Features:</h5>
                    <ul className="space-y-1">
                      {test.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <div className="w-1.5 h-1.5 bg-accent rounded-full"></div>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* GPU Testing Benefits */}
        <div className="mb-16">
          <Card variant="tech" className="p-8">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-2xl font-bold mb-6">Why Use Our Free Online GPU Benchmark?</h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-accent/20 rounded-lg mt-1">
                      <TrendingUp className="h-5 w-5 text-accent" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-1">No Software Installation Required</h4>
                      <p className="text-muted-foreground text-sm">
                        Run comprehensive GPU stress tests directly in your web browser without downloading or installing any software. 
                        Start testing your graphics card performance immediately.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-primary/20 rounded-lg mt-1">
                      <BarChart3 className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-1">Real-time Performance Monitoring</h4>
                      <p className="text-muted-foreground text-sm">
                        Monitor GPU temperature, utilization, memory usage, and frame rates in real-time during stress testing. 
                        Identify performance bottlenecks and thermal issues instantly.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-secondary/20 rounded-lg mt-1">
                      <Trophy className="h-5 w-5 text-secondary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-1">Compare Against Database</h4>
                      <p className="text-muted-foreground text-sm">
                        Compare your GPU benchmark results against thousands of other graphics cards in our comprehensive 
                        performance database. See how your hardware stacks up.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="bg-gradient-card p-6 rounded-lg border border-primary/20 shadow-glow-primary">
                  <h4 className="font-bold text-lg mb-3 gradient-text">Professional Results</h4>
                  <p className="text-muted-foreground text-sm mb-4">
                    Get detailed performance analysis including:
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-accent rounded-full"></div>
                      <span>Frame rate analysis</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span>Temperature monitoring</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-secondary rounded-full"></div>
                      <span>Power consumption metrics</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-accent rounded-full"></div>
                      <span>Stability assessment</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="inline-flex flex-col items-center space-y-4 p-8 bg-gradient-card border border-primary/20 rounded-lg shadow-glow-primary">
            <h3 className="text-2xl font-bold gradient-text">Test Your GPU Performance Now</h3>
            <p className="text-muted-foreground max-w-2xl">
              Join thousands of users who trust our free online GPU stress testing tool. Benchmark your graphics card 
              performance and compare results with our comprehensive database.
            </p>
            <Button variant="hero" size="xl" className="mt-4" onClick={handleBenchmarkClick}>
              <BarChart3 className="w-5 h-5 mr-2" />
              Start Free GPU Benchmark Test
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GPUBenchmarkSection;