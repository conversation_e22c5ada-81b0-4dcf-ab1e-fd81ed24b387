import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import HeroSection from "@/components/home/<USER>";
import TestInterface from "@/components/test/TestInterface";
import GPUTestingGuide from "@/components/home/<USER>";
import GPUBenchmarkSection from "@/components/home/<USER>";
import TechnicalDetails from "@/components/home/<USER>";
import SEO from "@/components/SEO";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <SEO 
        title="Free Online GPU Stress Test Tool - Cznull Github Volumeshader_bm test"
        description="Free online GPU stress test tool to benchmark and test your graphics card performance. Professional GPU testing with real-time monitoring and analysis."
        keywords="gpu stress test, graphics card test, free online gpu benchmark, gpu performance test, graphics card stress test, webgl benchmark"
        canonical="https://gpustresstes.com/"
      />
      <Navbar />
      <main>
        <HeroSection />
        <div data-testid="test-section">
          <TestInterface />
        </div>
        <GPUTestingGuide />
        <GPUBenchmarkSection />
        <TechnicalDetails />
      </main>
      <Footer />
    </div>
  );
};

export default Index;
