import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { HelpCircle, MessageCircle, Play, Shield, Thermometer, Monitor } from "lucide-react";
import SEO from "@/components/SEO";

const FAQ = () => {
  const faqCategories = [
    {
      title: "Getting Started",
      icon: Play,
      questions: [
        {
          question: "How do I start testing my GPU for free?",
          answer: "Simply visit our homepage and click the 'Start Free GPU Test' button. No software installation or registration required. Our web-based GPU stress test will automatically detect your graphics card and begin comprehensive performance analysis in your browser."
        },
        {
          question: "Do I need to download any software for GPU stress testing?",
          answer: "No! Our free online GPU stress test runs entirely in your web browser using advanced WebGL technology. This means you can test your graphics card immediately without downloading, installing, or configuring any additional software."
        },
        {
          question: "Which web browsers support GPU stress testing?",
          answer: "Our free GPU testing tool works on all modern browsers including Chrome 90+, Firefox 88+, Safari 14+, and Edge 90+. For the best testing experience, ensure your browser has hardware acceleration enabled and is updated to the latest version."
        },
        {
          question: "How long should I run the GPU stress test?",
          answer: "For basic stability testing, run the test for 10-15 minutes. For comprehensive stress testing, 30-60 minutes is recommended. Always monitor temperatures and stop testing if your GPU exceeds safe thermal limits (typically 85°C for most cards)."
        }
      ]
    },
    {
      title: "Safety & Monitoring",
      icon: Shield,
      questions: [
        {
          question: "Is GPU stress testing safe for my graphics card?",
          answer: "Yes, when done properly. Our free online GPU stress test includes built-in safety monitoring and will alert you if dangerous conditions are detected. Always ensure adequate cooling and monitor temperatures. Stop testing immediately if temperatures exceed manufacturer specifications."
        },
        {
          question: "What temperature is considered safe during GPU stress testing?",
          answer: "Most modern GPUs can safely operate up to 83-87°C under load. However, for prolonged stress testing, keeping temperatures below 80°C is ideal. Our monitoring system will warn you if temperatures approach dangerous levels during testing."
        },
        {
          question: "Can GPU stress testing damage my graphics card?",
          answer: "Modern GPUs have built-in thermal protection that prevents damage from overheating. However, running stress tests on already damaged or inadequately cooled hardware could potentially cause issues. Always ensure proper cooling before testing."
        },
        {
          question: "Should I overclock before running stress tests?",
          answer: "Only overclock if you're experienced and have adequate cooling. Start stress testing at stock settings first. If you're testing overclocked settings, increase speeds gradually and monitor stability carefully with our real-time monitoring tools."
        }
      ]
    },
    {
      title: "Technical Questions",
      icon: Monitor,
      questions: [
        {
          question: "What types of GPU tests does your tool perform?",
          answer: "Our comprehensive free GPU testing suite includes 3D rendering benchmarks, compute performance tests, memory stress testing, thermal monitoring, and stability analysis. We test various workloads to provide complete graphics card performance evaluation."
        },
        {
          question: "Can I test multiple GPUs simultaneously?",
          answer: "Our tool will automatically detect and test your primary graphics card. For multi-GPU setups (SLI/CrossFire), testing focuses on the primary GPU. Each GPU should be tested individually for most accurate results."
        },
        {
          question: "Why are my GPU benchmark results different from other tools?",
          answer: "Benchmark results can vary based on testing methodology, drivers, system configuration, and thermal conditions. Our free online GPU test uses standardized WebGL workloads, which may differ from native DirectX or OpenGL applications."
        },
        {
          question: "What GPU performance metrics does your tool measure?",
          answer: "We measure frame rates, GPU utilization, VRAM usage, temperature, power consumption estimates, rendering quality, and stability metrics. Our comprehensive analysis provides detailed insights into your graphics card's performance characteristics."
        }
      ]
    },
    {
      title: "Troubleshooting",
      icon: HelpCircle,
      questions: [
        {
          question: "The GPU stress test won't start - what should I do?",
          answer: "Ensure your browser supports WebGL 2.0 and hardware acceleration is enabled. Try refreshing the page, updating your browser, or updating your graphics drivers. Some corporate firewalls may block WebGL applications."
        },
        {
          question: "My GPU temperatures seem too high during testing",
          answer: "High temperatures during stress testing are normal, but if they exceed 85°C, stop the test immediately. Check that your GPU fans are working, clean dust from heatsinks, ensure adequate case ventilation, and verify your power supply can handle the load."
        },
        {
          question: "The test results seem inaccurate or inconsistent",
          answer: "Ensure no other applications are using your GPU during testing. Close games, video players, and other graphics-intensive programs. Run the test multiple times for consistency and ensure your system isn't thermal throttling."
        },
        {
          question: "Can I compare my results with other users?",
          answer: "Yes! Our free GPU benchmark database allows you to compare your results with thousands of other graphics cards. You can see how your GPU performs relative to similar hardware and identify potential performance issues."
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <SEO 
        title="GPU Testing FAQ - Common Questions About Graphics Card Stress Testing"
        description="Find answers to frequently asked questions about GPU stress testing, graphics card benchmarking, and performance testing. Expert guidance for safe GPU testing."
        keywords="gpu testing faq, graphics card questions, gpu stress test help, gpu benchmark guide, graphics card testing tips"
        canonical="https://gpustresstes.com/faq"
      />
      <Navbar />
      
      <main className="pt-20">
        {/* Header Section */}
        <section className="py-20 bg-gradient-to-b from-background to-card/20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-card border border-primary/20 rounded-full text-sm font-medium shadow-glow-primary mb-6">
              <HelpCircle className="w-4 h-4 mr-2 text-primary" />
              Frequently Asked Questions
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">GPU Stress Testing</span> FAQ
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Find answers to common questions about our free online GPU stress testing tool. 
              Learn how to safely and effectively test your graphics card performance.
            </p>
          </div>
        </section>

        {/* FAQ Categories */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="space-y-12">
              {faqCategories.map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <Card variant="tech" className="mb-6">
                    <CardHeader>
                      <div className="flex items-center space-x-3">
                        <div className="p-3 bg-gradient-primary rounded-lg shadow-glow-primary">
                          <category.icon className="h-6 w-6 text-primary-foreground" />
                        </div>
                        <CardTitle className="text-2xl">{category.title}</CardTitle>
                      </div>
                    </CardHeader>
                  </Card>
                  
                  <Accordion type="single" collapsible className="space-y-4">
                    {category.questions.map((faq, faqIndex) => (
                      <AccordionItem 
                        key={faqIndex} 
                        value={`${categoryIndex}-${faqIndex}`}
                        className="bg-gradient-card border border-primary/20 rounded-lg px-6 shadow-tech"
                      >
                        <AccordionTrigger className="text-left hover:text-primary transition-colors">
                          <span className="font-medium">{faq.question}</span>
                        </AccordionTrigger>
                        <AccordionContent className="text-muted-foreground leading-relaxed pt-2">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Quick Tips */}
        <section className="py-16 bg-gradient-to-b from-card/20 to-background">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold text-center mb-8 gradient-text">
              Quick GPU Testing Tips
            </h2>
            <div className="grid md:grid-cols-3 gap-6">
              <Card variant="glow" className="text-center p-6">
                <div className="p-3 bg-gradient-primary rounded-full w-fit mx-auto mb-4 shadow-glow-primary">
                  <Thermometer className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="font-semibold mb-2">Monitor Temperature</h3>
                <p className="text-sm text-muted-foreground">
                  Always watch GPU temperatures during stress testing. 
                  Stop if temperatures exceed 85°C.
                </p>
              </Card>
              
              <Card variant="glow" className="text-center p-6">
                <div className="p-3 bg-gradient-secondary rounded-full w-fit mx-auto mb-4 shadow-glow-secondary">
                  <Shield className="h-6 w-6 text-secondary-foreground" />
                </div>
                <h3 className="font-semibold mb-2">Start Gradually</h3>
                <p className="text-sm text-muted-foreground">
                  Begin with shorter test durations and gradually 
                  increase if temperatures remain stable.
                </p>
              </Card>
              
              <Card variant="glow" className="text-center p-6">
                <div className="p-3 bg-gradient-primary rounded-full w-fit mx-auto mb-4 shadow-glow-primary">
                  <Monitor className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="font-semibold mb-2">Close Other Apps</h3>
                <p className="text-sm text-muted-foreground">
                  Close games and other GPU-intensive applications 
                  for accurate testing results.
                </p>
              </Card>
            </div>
          </div>
        </section>

        {/* Contact Support */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Card variant="tech" className="p-8">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <MessageCircle className="h-6 w-6 text-primary" />
                <h2 className="text-2xl font-bold gradient-text">Still Have Questions?</h2>
              </div>
              <p className="text-muted-foreground mb-6">
                Can't find the answer you're looking for? Our technical support team is here to help 
                with any questions about GPU stress testing, performance analysis, or troubleshooting.
              </p>
              <Button variant="hero" size="lg">
                <MessageCircle className="w-5 h-5 mr-2" />
                Contact Support
              </Button>
            </Card>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default FAQ;